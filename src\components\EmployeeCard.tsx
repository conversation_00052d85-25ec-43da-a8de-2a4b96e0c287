
import { Employee } from '../types/employee';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { User, Mail, Phone } from 'lucide-react';

interface EmployeeCardProps {
  employee: Employee;
  onEdit?: (employee: Employee) => void;
  onView?: (employee: Employee) => void;
}

export function EmployeeCard({ employee, onEdit, onView }: EmployeeCardProps) {
  const getRoleBadgeColor = (role: string) => {
    const colors = {
      doctor: 'bg-blue-100 text-blue-800',
      nurse: 'bg-green-100 text-green-800',
      pharmacist: 'bg-purple-100 text-purple-800',
      technician: 'bg-orange-100 text-orange-800',
      admin: 'bg-gray-100 text-gray-800',
      receptionist: 'bg-pink-100 text-pink-800',
      manager: 'bg-indigo-100 text-indigo-800',
    };
    return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getStatusBadgeColor = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      on_leave: 'bg-yellow-100 text-yellow-800',
      terminated: 'bg-red-100 text-red-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <Card className="p-6 card-shadow hover:shadow-lg transition-all duration-200 hover:scale-[1.02]">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
            <User className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold text-lg text-gray-900">
              {employee.firstName} {employee.lastName}
            </h3>
            <p className="text-gray-600">{employee.position}</p>
            <p className="text-sm text-gray-500">ID: {employee.employeeId}</p>
          </div>
        </div>
        <div className="flex flex-col space-y-2">
          <Badge className={getRoleBadgeColor(employee.role)}>
            {employee.role}
          </Badge>
          <Badge className={getStatusBadgeColor(employee.status)}>
            {employee.status}
          </Badge>
        </div>
      </div>

      <div className="mt-4 space-y-2">
        <div className="flex items-center text-sm text-gray-600">
          <Mail className="w-4 h-4 mr-2" />
          {employee.email}
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <Phone className="w-4 h-4 mr-2" />
          {employee.phone}
        </div>
      </div>

      <div className="mt-4 flex justify-between items-center">
        <div className="text-sm text-gray-500">
          <span className="font-medium">Dept:</span> {employee.department}
        </div>
        <div className="flex space-x-2">
          {onView && (
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onView(employee)}
            >
              Lihat
            </Button>
          )}
          {onEdit && (
            <Button 
              size="sm"
              onClick={() => onEdit(employee)}
            >
              Edit
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
}
