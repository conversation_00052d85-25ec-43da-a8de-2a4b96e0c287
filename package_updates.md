# Package Dependencies for PostgreSQL Migration

## Remove Supabase Dependencies
```bash
npm uninstall @supabase/supabase-js
```

## Add PostgreSQL and Authentication Dependencies
```bash
# PostgreSQL client
npm install pg @types/pg

# Authentication and security
npm install bcryptjs @types/bcryptjs
npm install jsonwebtoken @types/jsonwebtoken
npm install express-session
npm install cookie-parser

# Environment variables
npm install dotenv

# Optional: Database query builder/ORM
npm install drizzle-orm drizzle-kit
# OR
npm install prisma @prisma/client
```

## Environment Variables Setup
Create a `.env` file in your project root:

```env
# Database Configuration
DATABASE_URL=postgresql://app_user:your_password@localhost:5432/manajemen_karyawan
DB_HOST=localhost
DB_PORT=5432
DB_NAME=manajemen_karyawan
DB_USER=app_user
DB_PASSWORD=your_password

# Authentication
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
SESSION_SECRET=your-session-secret-here

# Application
NODE_ENV=development
PORT=8080
```

## Vite Configuration Update
Update `vite.config.ts` to load environment variables:

```typescript
import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  
  return {
    server: {
      host: "::",
      port: 8080,
    },
    plugins: [react()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    define: {
      'process.env': env
    }
  };
});
```
