// Database connection configuration
import { Pool, PoolClient } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'manajemen_karyawan',
  user: process.env.DB_USER || 'app_user',
  password: process.env.DB_PASSWORD || '',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,

  // Connection pool settings
  max: parseInt(process.env.DB_POOL_MAX || '20'), // Maximum number of clients in the pool
  min: parseInt(process.env.DB_POOL_MIN || '2'), // Minimum number of clients in the pool
  idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'), // Close idle clients after 30 seconds
  connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '5000'), // Return error after 5 seconds if connection could not be established
  acquireTimeoutMillis: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'), // Return error after 60 seconds if a connection could not be acquired

  // Query settings
  statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT || '30000'), // 30 seconds
  query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'), // 30 seconds

  // Keep alive settings
  keepAlive: true,
  keepAliveInitialDelayMillis: 10000,
};

// Create connection pool
export const pool = new Pool(dbConfig);

// Database query helper with performance monitoring
export async function query(text: string, params?: any[]): Promise<any> {
  const startTime = Date.now();
  const client = await pool.connect();

  try {
    const result = await client.query(text, params);

    // Log slow queries in development
    const duration = Date.now() - startTime;
    if (process.env.NODE_ENV === 'development' && duration > 1000) {
      console.warn(`Slow query detected (${duration}ms):`, text.substring(0, 100) + '...');
    }

    return result;
  } catch (error) {
    console.error('Database query error:', {
      query: text.substring(0, 100) + '...',
      params: params?.length ? `${params.length} parameters` : 'no parameters',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  } finally {
    client.release();
  }
}

// Transaction helper
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Test database connection
export async function testConnection(): Promise<boolean> {
  try {
    const result = await query('SELECT NOW()');
    console.log('✅ Database connected successfully at:', result.rows[0].now);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Get database pool status
export function getPoolStatus() {
  return {
    totalCount: pool.totalCount,
    idleCount: pool.idleCount,
    waitingCount: pool.waitingCount,
  };
}

// Database health check with detailed metrics
export async function healthCheck(): Promise<{
  status: 'healthy' | 'unhealthy';
  details: any;
}> {
  try {
    const startTime = Date.now();
    const result = await query('SELECT NOW() as current_time, version() as version');
    const responseTime = Date.now() - startTime;

    const poolStatus = getPoolStatus();

    return {
      status: 'healthy',
      details: {
        responseTime: `${responseTime}ms`,
        currentTime: result.rows[0].current_time,
        version: result.rows[0].version,
        pool: poolStatus,
        uptime: process.uptime()
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        pool: getPoolStatus()
      }
    };
  }
}

// Graceful shutdown
export async function closeConnection(): Promise<void> {
  try {
    console.log('🔄 Closing database connections...');
    console.log('📊 Final pool status:', getPoolStatus());
    await pool.end();
    console.log('📦 Database connection pool closed');
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
}

// Database types
export interface User {
  id: string;
  email: string;
  password_hash: string;
  email_confirmed: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Profile {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  role: 'admin' | 'doctor' | 'nurse' | 'pharmacist' | 'technician' | 'receptionist' | 'manager';
  created_at: Date;
  updated_at: Date;
}

export interface Employee {
  id: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'doctor' | 'nurse' | 'pharmacist' | 'technician' | 'receptionist' | 'manager';
  department: 'emergency' | 'surgery' | 'pediatrics' | 'cardiology' | 'orthopedics' | 'pharmacy' | 'laboratory' | 'radiology' | 'administration' | 'maintenance';
  position: string;
  join_date: Date;
  status: 'active' | 'inactive' | 'on_leave' | 'terminated';
  shift: 'morning' | 'afternoon' | 'night' | 'rotating' | 'regular';
  salary?: number;
  avatar?: string;
  address?: string;
  user_id?: string;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Schedule {
  id: string;
  employee_id: string;
  shift_date: Date;
  shift_type: 'morning' | 'afternoon' | 'night' | 'rotating' | 'regular';
  start_time: string;
  end_time: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  notes?: string;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
}

export interface LeaveRequest {
  id: string;
  employee_id: string;
  leave_type: string;
  start_date: Date;
  end_date: Date;
  reason?: string;
  status: string;
  approved_by?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Department {
  id: string;
  name: string;
  description?: string;
  manager_id?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions?: any;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Position {
  id: string;
  name: string;
  description?: string;
  department_id?: string;
  role_id?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}
