const bcrypt = require('bcryptjs');
const { Client } = require('pg');

async function createTestUser() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'manaj<PERSON><PERSON>_ka<PERSON><PERSON>',
    user: 'postgres',
    password: '11<PERSON><PERSON><PERSON>'
  });

  try {
    await client.connect();
    
    // Hash the password
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    // Create test user
    const userId = '550e8400-e29b-41d4-a716-446655440000';
    
    // Insert or update user
    await client.query(`
      INSERT INTO users (id, email, password_hash, email_confirmed, created_at, updated_at)
      VALUES ($1, $2, $3, true, NOW(), NOW())
      ON CONFLICT (email) DO UPDATE SET 
        password_hash = EXCLUDED.password_hash,
        updated_at = NOW()
    `, [userId, '<EMAIL>', hashedPassword]);
    
    // Insert or update profile
    await client.query(`
      INSERT INTO profiles (id, first_name, last_name, email, role, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      ON CONFLICT (id) DO UPDATE SET 
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        email = EXCLUDED.email,
        role = EXCLUDED.role,
        updated_at = NOW()
    `, [userId, 'Test', 'Admin', '<EMAIL>', 'admin']);
    
    console.log('Test user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await client.end();
  }
}

createTestUser();
