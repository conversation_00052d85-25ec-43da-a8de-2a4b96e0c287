
import { useAuth } from '@/hooks/useAuthNew';
import { EmployeeRole } from '@/types/employee';

interface UserProfile {
  role: EmployeeRole;
  first_name: string;
  last_name: string;
}

export function useRoleAccess() {
  const { user } = useAuth();

  // Get profile from user data (already included in auth response)
  const profile = user?.profile ? {
    role: user.profile.role,
    first_name: user.profile.first_name,
    last_name: user.profile.last_name,
  } as UserProfile : null;

  const isLoading = false; // No additional loading since profile is in user data

  const hasRole = (requiredRole: EmployeeRole): boolean => {
    return profile?.role === requiredRole;
  };

  const hasAnyRole = (requiredRoles: EmployeeRole[]): boolean => {
    return requiredRoles.includes(profile?.role as EmployeeRole);
  };

  const isAdmin = (): boolean => {
    return profile?.role === 'admin';
  };

  const canAccessManagement = (): boolean => {
    return hasAnyRole(['admin', 'manager']);
  };

  const canManageEmployees = (): boolean => {
    return hasAnyRole(['admin', 'manager']);
  };

  const canViewReports = (): boolean => {
    return hasAnyRole(['admin', 'manager', 'doctor']);
  };

  return {
    profile,
    isLoading,
    hasRole,
    hasAnyRole,
    isAdmin,
    canAccessManagement,
    canManageEmployees,
    canViewReports,
    userRole: profile?.role,
  };
}
