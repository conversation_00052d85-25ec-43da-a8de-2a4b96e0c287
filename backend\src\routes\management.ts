// Management routes for departments, roles, and positions
import { Router, Response } from 'express';
import { ManagementService } from '@/services/managementService';
import { authenticateToken, AuthRequest } from '@/middleware/auth';
import { requirePermission } from '@/middleware/permissions';
import { PERMISSIONS } from '@/services/permissionService';
import { validate, departmentSchemas, roleSchemas } from '@/middleware/validation';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// DEPARTMENT ROUTES

// Get all departments
router.get('/departments', requirePermission(PERMISSIONS.DEPARTMENT_READ), async (req: AuthRequest, res: Response) => {
  try {
    const departments = await ManagementService.getAllDepartments();
    res.json({ departments });
  } catch (error) {
    console.error('Get departments endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch departments' });
  }
});

// Get department by ID
router.get('/departments/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const department = await ManagementService.getDepartmentById(id);
    
    if (!department) {
      return res.status(404).json({ error: 'Department not found' });
    }

    res.json({ department });
  } catch (error) {
    console.error('Get department by ID endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch department' });
  }
});

// Create department (Admin only)
router.post('/departments', requirePermission(PERMISSIONS.DEPARTMENT_CREATE), validate(departmentSchemas.create), async (req: AuthRequest, res: Response) => {
  try {
    const departmentData = {
      name: req.body.name,
      description: req.body.description,
      manager_id: req.body.managerId,
      is_active: true,
    };

    const department = await ManagementService.createDepartment(departmentData);

    res.status(201).json({
      message: 'Department created successfully',
      department
    });
  } catch (error) {
    console.error('Create department endpoint error:', error);
    
    if (error instanceof Error && error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to create department' });
  }
});

// Update department (Admin only)
router.put('/departments/:id', requirePermission(PERMISSIONS.DEPARTMENT_UPDATE), validate(departmentSchemas.update), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const updateData: any = {};
    if (req.body.name !== undefined) updateData.name = req.body.name;
    if (req.body.description !== undefined) updateData.description = req.body.description;
    if (req.body.managerId !== undefined) updateData.manager_id = req.body.managerId;
    if (req.body.isActive !== undefined) updateData.is_active = req.body.isActive;

    const department = await ManagementService.updateDepartment(id, updateData);

    res.json({
      message: 'Department updated successfully',
      department
    });
  } catch (error) {
    console.error('Update department endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Department not found') {
        return res.status(404).json({ error: error.message });
      }
      if (error.message.includes('already exists')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to update department' });
  }
});

// Delete department (Admin only)
router.delete('/departments/:id', requirePermission(PERMISSIONS.DEPARTMENT_DELETE), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    await ManagementService.deleteDepartment(id);
    res.json({ message: 'Department deleted successfully' });
  } catch (error) {
    console.error('Delete department endpoint error:', error);
    
    if (error instanceof Error && error.message === 'Department not found') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to delete department' });
  }
});

// ROLE ROUTES

// Get all roles
router.get('/roles', requirePermission(PERMISSIONS.ROLE_READ), async (req: AuthRequest, res: Response) => {
  try {
    const roles = await ManagementService.getAllRoles();
    res.json({ roles });
  } catch (error) {
    console.error('Get roles endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch roles' });
  }
});

// Get role by ID
router.get('/roles/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const role = await ManagementService.getRoleById(id);
    
    if (!role) {
      return res.status(404).json({ error: 'Role not found' });
    }

    res.json({ role });
  } catch (error) {
    console.error('Get role by ID endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch role' });
  }
});

// Create role (Admin only)
router.post('/roles', requirePermission(PERMISSIONS.ROLE_CREATE), validate(roleSchemas.create), async (req: AuthRequest, res: Response) => {
  try {
    const roleData = {
      name: req.body.name,
      description: req.body.description,
      permissions: req.body.permissions,
      is_active: true,
    };

    const role = await ManagementService.createRole(roleData);

    res.status(201).json({
      message: 'Role created successfully',
      role
    });
  } catch (error) {
    console.error('Create role endpoint error:', error);
    
    if (error instanceof Error && error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to create role' });
  }
});

// Update role (Admin only)
router.put('/roles/:id', requirePermission(PERMISSIONS.ROLE_UPDATE), validate(roleSchemas.update), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const updateData: any = {};
    if (req.body.name !== undefined) updateData.name = req.body.name;
    if (req.body.description !== undefined) updateData.description = req.body.description;
    if (req.body.permissions !== undefined) updateData.permissions = req.body.permissions;
    if (req.body.isActive !== undefined) updateData.is_active = req.body.isActive;

    const role = await ManagementService.updateRole(id, updateData);

    res.json({
      message: 'Role updated successfully',
      role
    });
  } catch (error) {
    console.error('Update role endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Role not found') {
        return res.status(404).json({ error: error.message });
      }
      if (error.message.includes('already exists')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to update role' });
  }
});

// POSITION ROUTES

// Get all positions
router.get('/positions', requirePermission(PERMISSIONS.POSITION_READ), async (req: AuthRequest, res: Response) => {
  try {
    const positions = await ManagementService.getAllPositions();
    res.json({ positions });
  } catch (error) {
    console.error('Get positions endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch positions' });
  }
});

// Get position by ID
router.get('/positions/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const position = await ManagementService.getPositionById(id);
    
    if (!position) {
      return res.status(404).json({ error: 'Position not found' });
    }

    res.json({ position });
  } catch (error) {
    console.error('Get position by ID endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch position' });
  }
});

// Create position (Admin only)
router.post('/positions', requirePermission(PERMISSIONS.POSITION_CREATE), async (req: AuthRequest, res: Response) => {
  try {
    const positionData = {
      name: req.body.name,
      description: req.body.description,
      department_id: req.body.departmentId,
      role_id: req.body.roleId,
      is_active: true,
    };

    const position = await ManagementService.createPosition(positionData);

    res.status(201).json({
      message: 'Position created successfully',
      position
    });
  } catch (error) {
    console.error('Create position endpoint error:', error);
    
    if (error instanceof Error && error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to create position' });
  }
});

// Update position (Admin only)
router.put('/positions/:id', requirePermission(PERMISSIONS.POSITION_UPDATE), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const updateData: any = {};
    if (req.body.name !== undefined) updateData.name = req.body.name;
    if (req.body.description !== undefined) updateData.description = req.body.description;
    if (req.body.departmentId !== undefined) updateData.department_id = req.body.departmentId;
    if (req.body.roleId !== undefined) updateData.role_id = req.body.roleId;
    if (req.body.isActive !== undefined) updateData.is_active = req.body.isActive;

    const position = await ManagementService.updatePosition(id, updateData);

    res.json({
      message: 'Position updated successfully',
      position
    });
  } catch (error) {
    console.error('Update position endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Position not found') {
        return res.status(404).json({ error: error.message });
      }
      if (error.message.includes('already exists')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to update position' });
  }
});

export default router;
