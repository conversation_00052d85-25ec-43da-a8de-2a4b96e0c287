
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from './Sidebar';
import { useAuth } from '@/hooks/useAuthNew';
import { useRoleAccess } from '@/hooks/useRoleAccess';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LogOut, User } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const { user, signOut } = useAuth();
  const { profile } = useRoleAccess();

  const roleLabels = {
    admin: 'Administrator',
    doctor: '<PERSON><PERSON><PERSON>',
    nurse: '<PERSON><PERSON><PERSON>',
    pharmacist: '<PERSON><PERSON><PERSON><PERSON>',
    technician: '<PERSON><PERSON><PERSON><PERSON>',
    receptionist: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    manager: '<PERSON><PERSON><PERSON>'
  };

  const roleColors = {
    admin: 'bg-red-100 text-red-800',
    doctor: 'bg-blue-100 text-blue-800',
    nurse: 'bg-green-100 text-green-800',
    pharmacist: 'bg-purple-100 text-purple-800',
    technician: 'bg-orange-100 text-orange-800',
    receptionist: 'bg-pink-100 text-pink-800',
    manager: 'bg-yellow-100 text-yellow-800'
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gray-50">
        <AppSidebar />
        <main className="flex-1 overflow-auto">
          <div className="border-b border-gray-200 bg-white px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h1 className="text-lg font-semibold text-gray-900">
                  Sistem Manajemen Karyawan
                </h1>
              </div>
              {user && (
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-gray-500" />
                      <div className="flex flex-col">
                        <span className="text-sm font-medium text-gray-700">
                          {profile ? `${profile.first_name} ${profile.last_name}` : user.email}
                        </span>
                        <span className="text-xs text-gray-500">{user.email}</span>
                      </div>
                    </div>
                    {profile?.role && (
                      <Badge className={roleColors[profile.role as keyof typeof roleColors]}>
                        {roleLabels[profile.role as keyof typeof roleLabels]}
                      </Badge>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={signOut}
                    className="flex items-center space-x-2"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Keluar</span>
                  </Button>
                </div>
              )}
            </div>
          </div>
          {children}
        </main>
      </div>
    </SidebarProvider>
  );
}
