
import { useParams, useNavigate } from 'react-router-dom';
import { useEmployees } from '@/hooks/useEmployeesNew';
import { EmployeeDetail } from '@/components/EmployeeDetail';
import { PhotoUpload } from '@/components/PhotoUpload';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useState } from 'react';

export default function EmployeeProfile() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { employees, isLoading, updateEmployee } = useEmployees();
  const [showPhotoDialog, setShowPhotoDialog] = useState(false);

  const employee = employees.find(emp => emp.id === id);

  const handlePhotoUpdate = (photoUrl: string) => {
    if (employee) {
      updateEmployee({
        ...employee,
        avatar: photoUrl,
      });
      setShowPhotoDialog(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="p-6">
        <Card className="p-6 text-center">
          <p className="text-gray-600">Karyawan tidak ditemukan</p>
          <Button onClick={() => navigate('/employees')} className="mt-4">
            Kembali ke Daftar Karyawan
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6 flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => navigate('/employees')}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Kembali</span>
        </Button>
        
        <Button
          onClick={() => navigate(`/employees/${employee.id}/edit`)}
          className="flex items-center space-x-2"
        >
          <Edit className="w-4 h-4" />
          <span>Edit Profil</span>
        </Button>
      </div>

      <EmployeeDetail
        employee={employee}
        onEdit={() => navigate(`/employees/${employee.id}/edit`)}
        onUploadPhoto={() => setShowPhotoDialog(true)}
      />

      <Dialog open={showPhotoDialog} onOpenChange={setShowPhotoDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Upload Foto Profil</DialogTitle>
          </DialogHeader>
          <PhotoUpload
            currentPhoto={employee.avatar}
            employeeName={`${employee.firstName} ${employee.lastName}`}
            onPhotoUpdate={handlePhotoUpdate}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
