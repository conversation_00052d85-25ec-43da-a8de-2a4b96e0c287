
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON>hart, Pie, Cell } from 'recharts';
import { Schedule } from '@/types/schedule';
import { TrendingUp, Clock, Users, Calendar } from 'lucide-react';

interface ShiftRotationAnalyzerProps {
  schedules: Schedule[];
}

export function ShiftRotationAnalyzer({ schedules }: ShiftRotationAnalyzerProps) {
  // Analisis coverage per shift
  const shiftCoverage = schedules.reduce((acc, schedule) => {
    const shift = schedule.shiftType;
    acc[shift] = (acc[shift] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const coverageData = [
    { name: 'Pagi', value: shiftCoverage.morning || 0, color: '#fbbf24' },
    { name: '<PERSON><PERSON>', value: shiftCoverage.afternoon || 0, color: '#3b82f6' },
    { name: '<PERSON><PERSON>', value: shiftCoverage.night || 0, color: '#8b5cf6' },
    { name: 'Reguler', value: shiftCoverage.regular || 0, color: '#10b981' },
  ];

  // Analisis per hari dalam seminggu
  const weekdayAnalysis = schedules.reduce((acc, schedule) => {
    const date = new Date(schedule.shiftDate);
    const dayName = date.toLocaleDateString('id-ID', { weekday: 'long' });
    acc[dayName] = (acc[dayName] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const weekdayData = Object.entries(weekdayAnalysis).map(([day, count]) => ({
    day,
    count,
  }));

  // Analisis beban kerja per perawat
  const workloadAnalysis = schedules.reduce((acc, schedule) => {
    const employeeName = schedule.employee ? 
      `${schedule.employee.firstName} ${schedule.employee.lastName}` : 
      'Unknown';
    acc[employeeName] = (acc[employeeName] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const workloadData = Object.entries(workloadAnalysis)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count);

  const totalSchedules = schedules.length;
  const uniqueEmployees = new Set(schedules.map(s => s.employeeId)).size;
  const averageWorkload = totalSchedules / (uniqueEmployees || 1);

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Total Jadwal</p>
                <p className="text-2xl font-bold">{totalSchedules}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Perawat Aktif</p>
                <p className="text-2xl font-bold">{uniqueEmployees}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">Rata-rata Beban</p>
                <p className="text-2xl font-bold">{averageWorkload.toFixed(1)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm text-gray-600">Coverage Rate</p>
                <p className="text-2xl font-bold">
                  {((totalSchedules / (uniqueEmployees * 6)) * 100).toFixed(0)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Shift Coverage Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Distribusi Coverage per Shift</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={coverageData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {coverageData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Detail Coverage:</h4>
              {coverageData.map((item) => (
                <div key={item.name} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded"
                      style={{ backgroundColor: item.color }}
                    />
                    <span>{item.name}</span>
                  </div>
                  <Badge variant="outline">{item.value} jadwal</Badge>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Workload Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Distribusi Beban Kerja per Perawat</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={workloadData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name" 
                angle={-45}
                textAnchor="end"
                height={80}
                interval={0}
              />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#3b82f6" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Weekly Pattern */}
      <Card>
        <CardHeader>
          <CardTitle>Pola Jadwal per Hari dalam Seminggu</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={weekdayData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}
