// Schedules hook using API endpoints
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { scheduleApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Schedule } from '@/types/schedule';

// Transform API response to frontend format
const transformScheduleFromApi = (apiSchedule: any): Schedule => ({
  id: apiSchedule.id,
  employeeId: apiSchedule.employee_id,
  shiftDate: apiSchedule.shift_date,
  shiftType: apiSchedule.shift_type,
  startTime: apiSchedule.start_time,
  endTime: apiSchedule.end_time,
  status: apiSchedule.status,
  notes: apiSchedule.notes,
  createdBy: apiSchedule.created_by,
  createdAt: apiSchedule.created_at,
  updatedAt: apiSchedule.updated_at,
  employee: apiSchedule.employee ? {
    firstName: apiSchedule.employee.first_name,
    lastName: apiSchedule.employee.last_name,
    department: apiSchedule.employee.department,
    position: apiSchedule.employee.position,
  } : undefined,
});

// Transform frontend data to API format
const transformScheduleToApi = (schedule: Partial<Schedule>) => ({
  employeeId: schedule.employeeId,
  shiftDate: schedule.shiftDate,
  shiftType: schedule.shiftType,
  startTime: schedule.startTime,
  endTime: schedule.endTime,
  status: schedule.status,
  notes: schedule.notes,
});

export function useSchedules(params?: {
  employeeId?: string;
  startDate?: string;
  endDate?: string;
}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all schedules
  const { data: schedulesData, isLoading, error } = useQuery({
    queryKey: ['schedules', params],
    queryFn: () => scheduleApi.getAll(params),
  });

  const schedules = (schedulesData?.schedules || []).map(transformScheduleFromApi);

  // Create schedule mutation
  const createScheduleMutation = useMutation({
    mutationFn: async (scheduleData: Partial<Schedule>) => {
      const apiData = transformScheduleToApi(scheduleData);
      const response = await scheduleApi.create(apiData);
      return transformScheduleFromApi(response.schedule);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Berhasil",
        description: "Jadwal berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menambahkan jadwal",
        variant: "destructive",
      });
    },
  });

  // Update schedule mutation
  const updateScheduleMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Schedule> }) => {
      const apiData = transformScheduleToApi(data);
      const response = await scheduleApi.update(id, apiData);
      return transformScheduleFromApi(response.schedule);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Berhasil",
        description: "Jadwal berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui jadwal",
        variant: "destructive",
      });
    },
  });

  // Delete schedule mutation
  const deleteScheduleMutation = useMutation({
    mutationFn: async (id: string) => {
      await scheduleApi.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Berhasil",
        description: "Jadwal berhasil dihapus",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menghapus jadwal",
        variant: "destructive",
      });
    },
  });

  return {
    schedules,
    isLoading,
    error,
    createSchedule: createScheduleMutation.mutate,
    updateSchedule: updateScheduleMutation.mutate,
    deleteSchedule: deleteScheduleMutation.mutate,
    isCreating: createScheduleMutation.isPending,
    isUpdating: updateScheduleMutation.isPending,
    isDeleting: deleteScheduleMutation.isPending,
  };
}

// Hook for getting a single schedule
export function useSchedule(id: string) {
  return useQuery({
    queryKey: ['schedule', id],
    queryFn: async () => {
      const response = await scheduleApi.getById(id);
      return transformScheduleFromApi(response.schedule);
    },
    enabled: !!id,
  });
}

// Hook for schedule statistics
export function useScheduleStatistics(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return useQuery({
    queryKey: ['schedule-statistics', params],
    queryFn: async () => {
      return await scheduleApi.getStatistics(params);
    },
  });
}
