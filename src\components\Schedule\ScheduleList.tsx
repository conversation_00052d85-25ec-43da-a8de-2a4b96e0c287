
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar } from 'lucide-react';
import { Schedule } from '@/types/schedule';

interface ScheduleListProps {
  schedules: Schedule[];
}

export function ScheduleList({ schedules }: ScheduleListProps) {
  const getShiftTypeColor = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'bg-yellow-100 text-yellow-800';
      case 'afternoon': return 'bg-blue-100 text-blue-800';
      case 'night': return 'bg-purple-100 text-purple-800';
      case 'regular': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getShiftTypeLabel = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'Pagi';
      case 'afternoon': return 'Siang';
      case 'night': return 'Malam';
      case 'regular': return 'Reguler';
      case 'rotating': return 'Bergilir';
      default: return shiftType;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'no_show': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'scheduled': return 'Terjadwal';
      case 'completed': return 'Selesai';
      case 'cancelled': return 'Dibatalkan';
      case 'no_show': return 'Tidak Hadir';
      default: return status;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Daftar Jadwal Kerja</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {schedules.map((schedule) => (
            <div key={schedule.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <div>
                  <p className="font-medium">
                    {schedule.employee?.firstName} {schedule.employee?.lastName}
                  </p>
                  <p className="text-sm text-gray-600">
                    {schedule.employee?.department} - {schedule.employee?.position}
                  </p>
                </div>
                <div className="text-sm">
                  <p className="font-medium">{new Date(schedule.shiftDate).toLocaleDateString('id-ID')}</p>
                  <p className="text-gray-600">{schedule.startTime} - {schedule.endTime}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={getShiftTypeColor(schedule.shiftType)}>
                  {getShiftTypeLabel(schedule.shiftType)}
                </Badge>
                <Badge className={getStatusColor(schedule.status)}>
                  {getStatusLabel(schedule.status)}
                </Badge>
              </div>
            </div>
          ))}
          
          {schedules.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Belum ada jadwal yang dibuat</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
