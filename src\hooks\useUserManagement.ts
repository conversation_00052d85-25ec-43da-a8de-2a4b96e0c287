// User management hook for admin operations
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

export function useUserManagement() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get users with filtering and pagination
  const useUsers = (params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }) => {
    return useQuery({
      queryKey: ['admin-users', params],
      queryFn: async () => {
        const response = await adminApi.getUsers(params);
        return response;
      },
    });
  };

  // Get single user
  const useUser = (id: string) => {
    return useQuery({
      queryKey: ['admin-user', id],
      queryFn: async () => {
        const response = await adminApi.getUser(id);
        return response.user;
      },
      enabled: !!id,
    });
  };

  // Get user statistics
  const useUserStats = () => {
    return useQuery({
      queryKey: ['admin-stats'],
      queryFn: async () => {
        const response = await adminApi.getStats();
        return response.statistics;
      },
    });
  };

  // Get user activity logs
  const useUserActivity = (id: string, params?: { limit?: number; offset?: number }) => {
    return useQuery({
      queryKey: ['admin-user-activity', id, params],
      queryFn: async () => {
        const response = await adminApi.getUserActivity(id, params);
        return response.logs;
      },
      enabled: !!id,
    });
  };

  // Get all activity logs
  const useActivityLogs = (params?: {
    limit?: number;
    offset?: number;
    userId?: string;
    action?: string;
    resource?: string;
    startDate?: string;
    endDate?: string;
  }) => {
    return useQuery({
      queryKey: ['admin-activity-logs', params],
      queryFn: async () => {
        const response = await adminApi.getActivityLogs(params);
        return response.logs;
      },
    });
  };

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: async (userData: {
      email: string;
      password: string;
      firstName?: string;
      lastName?: string;
      role?: string;
      emailConfirmed?: boolean;
    }) => {
      const response = await adminApi.createUser(userData);
      return response.user;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      queryClient.invalidateQueries({ queryKey: ['admin-stats'] });
      toast({
        title: "Berhasil",
        description: "User berhasil dibuat",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal membuat user",
        variant: "destructive",
      });
    },
  });

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: async ({ id, data }: {
      id: string;
      data: {
        firstName?: string;
        lastName?: string;
        email?: string;
        role?: string;
        emailConfirmed?: boolean;
      };
    }) => {
      const response = await adminApi.updateUser(id, data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      queryClient.invalidateQueries({ queryKey: ['admin-user'] });
      toast({
        title: "Berhasil",
        description: "User berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui user",
        variant: "destructive",
      });
    },
  });

  // Update user status mutation
  const updateUserStatusMutation = useMutation({
    mutationFn: async ({ id, status }: {
      id: string;
      status: { suspended?: boolean };
    }) => {
      const response = await adminApi.updateUserStatus(id, status);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      queryClient.invalidateQueries({ queryKey: ['admin-user'] });
      queryClient.invalidateQueries({ queryKey: ['admin-stats'] });
      toast({
        title: "Berhasil",
        description: "Status user berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui status user",
        variant: "destructive",
      });
    },
  });

  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await adminApi.deleteUser(id);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      queryClient.invalidateQueries({ queryKey: ['admin-stats'] });
      toast({
        title: "Berhasil",
        description: "User berhasil dihapus",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menghapus user",
        variant: "destructive",
      });
    },
  });

  // Bulk create users mutation
  const bulkCreateUsersMutation = useMutation({
    mutationFn: async (users: Array<{
      email: string;
      password: string;
      firstName?: string;
      lastName?: string;
      role?: string;
    }>) => {
      const response = await adminApi.bulkCreateUsers(users);
      return response;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      queryClient.invalidateQueries({ queryKey: ['admin-stats'] });
      toast({
        title: "Berhasil",
        description: `Bulk create selesai. Berhasil: ${data.created.length}, Error: ${data.errors.length}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal bulk create users",
        variant: "destructive",
      });
    },
  });

  // Bulk update users mutation
  const bulkUpdateUsersMutation = useMutation({
    mutationFn: async (updates: Array<{
      id: string;
      suspended?: boolean;
      emailConfirmed?: boolean;
      role?: string;
    }>) => {
      const response = await adminApi.bulkUpdateUsers(updates);
      return response;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      queryClient.invalidateQueries({ queryKey: ['admin-stats'] });
      toast({
        title: "Berhasil",
        description: `Bulk update selesai. Berhasil: ${data.updated.length}, Error: ${data.errors.length}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal bulk update users",
        variant: "destructive",
      });
    },
  });

  // Bulk delete users mutation
  const bulkDeleteUsersMutation = useMutation({
    mutationFn: async (userIds: string[]) => {
      const response = await adminApi.bulkDeleteUsers(userIds);
      return response;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      queryClient.invalidateQueries({ queryKey: ['admin-stats'] });
      toast({
        title: "Berhasil",
        description: `Bulk delete selesai. Berhasil: ${data.deleted.length}, Error: ${data.errors.length}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal bulk delete users",
        variant: "destructive",
      });
    },
  });

  // Reset password mutation
  const resetPasswordMutation = useMutation({
    mutationFn: async ({ id, newPassword }: { id: string; newPassword: string }) => {
      const response = await adminApi.resetUserPassword(id, newPassword);
      return response;
    },
    onSuccess: () => {
      toast({
        title: "Berhasil",
        description: "Password berhasil direset",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal reset password",
        variant: "destructive",
      });
    },
  });

  // Unlock user mutation
  const unlockUserMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await adminApi.unlockUser(id);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      queryClient.invalidateQueries({ queryKey: ['admin-user'] });
      toast({
        title: "Berhasil",
        description: "User berhasil di-unlock",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal unlock user",
        variant: "destructive",
      });
    },
  });

  return {
    // Queries
    useUsers,
    useUser,
    useUserStats,
    useUserActivity,
    useActivityLogs,

    // Mutations
    createUser: createUserMutation.mutate,
    updateUser: updateUserMutation.mutate,
    updateUserStatus: updateUserStatusMutation.mutate,
    deleteUser: deleteUserMutation.mutate,
    bulkCreateUsers: bulkCreateUsersMutation.mutate,
    bulkUpdateUsers: bulkUpdateUsersMutation.mutate,
    bulkDeleteUsers: bulkDeleteUsersMutation.mutate,
    resetPassword: resetPasswordMutation.mutate,
    unlockUser: unlockUserMutation.mutate,

    // Loading states
    isCreating: createUserMutation.isPending,
    isUpdating: updateUserMutation.isPending,
    isDeleting: deleteUserMutation.isPending,
    isBulkCreating: bulkCreateUsersMutation.isPending,
    isBulkUpdating: bulkUpdateUsersMutation.isPending,
    isBulkDeleting: bulkDeleteUsersMutation.isPending,
    isResettingPassword: resetPasswordMutation.isPending,
    isUnlocking: unlockUserMutation.isPending,
  };
}
