# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=manajemen_karyawan
DB_USER=app_user
DB_PASSWORD=your_secure_password
DATABASE_URL=postgresql://app_user:your_secure_password@localhost:5432/manajemen_karyawan

# Authentication
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret-key
REFRESH_TOKEN_EXPIRES_IN=30d

# Server Configuration
NODE_ENV=development
PORT=3001
API_PREFIX=/api/v1

# CORS Configuration
FRONTEND_URL=http://localhost:8080
ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key

# Logging
LOG_LEVEL=info
