// Request validation middleware using Joi
import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

// Generic validation middleware
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      res.status(400).json({ 
        error: 'Validation error', 
        details: errorMessage,
        fields: error.details.map(detail => detail.path.join('.'))
      });
      return;
    }
    
    next();
  };
};

// Authentication schemas
export const authSchemas = {
  register: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
    password: Joi.string()
      .min(8)
      .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
      .required()
      .messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
        'any.required': 'Password is required'
      }),
    firstName: Joi.string().min(2).max(50).optional().messages({
      'string.min': 'First name must be at least 2 characters long',
      'string.max': 'First name cannot exceed 50 characters'
    }),
    lastName: Joi.string().min(2).max(50).optional().messages({
      'string.min': 'Last name must be at least 2 characters long',
      'string.max': 'Last name cannot exceed 50 characters'
    }),
    role: Joi.string().valid('admin', 'doctor', 'nurse', 'pharmacist', 'technician', 'receptionist', 'manager').optional()
  }),

  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': 'Current password is required'
    }),
    newPassword: Joi.string()
      .min(8)
      .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
      .required()
      .messages({
        'string.min': 'New password must be at least 8 characters long',
        'string.pattern.base': 'New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
        'any.required': 'New password is required'
      })
  }),

  forgotPassword: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    })
  }),

  resetPassword: Joi.object({
    token: Joi.string().required().messages({
      'any.required': 'Reset token is required'
    }),
    newPassword: Joi.string()
      .min(8)
      .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
      .required()
      .messages({
        'string.min': 'New password must be at least 8 characters long',
        'string.pattern.base': 'New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
        'any.required': 'New password is required'
      })
  })
};

// Employee schemas
export const employeeSchemas = {
  create: Joi.object({
    employeeId: Joi.string().required().messages({
      'any.required': 'Employee ID is required'
    }),
    firstName: Joi.string().min(2).max(50).required(),
    lastName: Joi.string().min(2).max(50).required(),
    email: Joi.string().email().required(),
    phone: Joi.string().optional(),
    role: Joi.string().valid('admin', 'doctor', 'nurse', 'pharmacist', 'technician', 'receptionist', 'manager').required(),
    department: Joi.string().valid('emergency', 'surgery', 'pediatrics', 'cardiology', 'orthopedics', 'pharmacy', 'laboratory', 'radiology', 'administration', 'maintenance').required(),
    position: Joi.string().min(2).max(100).required(),
    joinDate: Joi.date().required(),
    status: Joi.string().valid('active', 'inactive', 'on_leave', 'terminated').default('active'),
    shift: Joi.string().valid('morning', 'afternoon', 'night', 'rotating', 'regular').required(),
    salary: Joi.number().positive().optional(),
    avatar: Joi.string().uri().optional(),
    address: Joi.string().max(500).optional()
  }),

  update: Joi.object({
    employeeId: Joi.string().optional(),
    firstName: Joi.string().min(2).max(50).optional(),
    lastName: Joi.string().min(2).max(50).optional(),
    email: Joi.string().email().optional(),
    phone: Joi.string().optional(),
    role: Joi.string().valid('admin', 'doctor', 'nurse', 'pharmacist', 'technician', 'receptionist', 'manager').optional(),
    department: Joi.string().valid('emergency', 'surgery', 'pediatrics', 'cardiology', 'orthopedics', 'pharmacy', 'laboratory', 'radiology', 'administration', 'maintenance').optional(),
    position: Joi.string().min(2).max(100).optional(),
    joinDate: Joi.date().optional(),
    status: Joi.string().valid('active', 'inactive', 'on_leave', 'terminated').optional(),
    shift: Joi.string().valid('morning', 'afternoon', 'night', 'rotating', 'regular').optional(),
    salary: Joi.number().positive().optional(),
    avatar: Joi.string().uri().optional(),
    address: Joi.string().max(500).optional()
  })
};

// Schedule schemas
export const scheduleSchemas = {
  create: Joi.object({
    employeeId: Joi.string().uuid().required(),
    shiftDate: Joi.date().required(),
    shiftType: Joi.string().valid('morning', 'afternoon', 'night', 'rotating', 'regular').required(),
    startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required().messages({
      'string.pattern.base': 'Start time must be in HH:MM format'
    }),
    endTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required().messages({
      'string.pattern.base': 'End time must be in HH:MM format'
    }),
    status: Joi.string().valid('scheduled', 'completed', 'cancelled', 'no_show').default('scheduled'),
    notes: Joi.string().max(500).optional()
  }),

  update: Joi.object({
    employeeId: Joi.string().uuid().optional(),
    shiftDate: Joi.date().optional(),
    shiftType: Joi.string().valid('morning', 'afternoon', 'night', 'rotating', 'regular').optional(),
    startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    endTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    status: Joi.string().valid('scheduled', 'completed', 'cancelled', 'no_show').optional(),
    notes: Joi.string().max(500).optional()
  })
};

// Leave request schemas
export const leaveRequestSchemas = {
  create: Joi.object({
    employeeId: Joi.string().uuid().required(),
    leaveType: Joi.string().min(2).max(50).required(),
    startDate: Joi.date().required(),
    endDate: Joi.date().min(Joi.ref('startDate')).required().messages({
      'date.min': 'End date must be after start date'
    }),
    reason: Joi.string().max(500).optional(),
    status: Joi.string().valid('pending', 'approved', 'rejected').default('pending')
  }),

  update: Joi.object({
    leaveType: Joi.string().min(2).max(50).optional(),
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
    reason: Joi.string().max(500).optional(),
    status: Joi.string().valid('pending', 'approved', 'rejected').optional()
  })
};

// Profile schemas
export const profileSchemas = {
  update: Joi.object({
    firstName: Joi.string().min(2).max(50).optional(),
    lastName: Joi.string().min(2).max(50).optional(),
    email: Joi.string().email().optional(),
    role: Joi.string().valid('admin', 'doctor', 'nurse', 'pharmacist', 'technician', 'receptionist', 'manager').optional()
  })
};

// Department schemas
export const departmentSchemas = {
  create: Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    managerId: Joi.string().uuid().optional()
  }),

  update: Joi.object({
    name: Joi.string().min(2).max(100).optional(),
    description: Joi.string().max(500).optional(),
    managerId: Joi.string().uuid().optional(),
    isActive: Joi.boolean().optional()
  })
};

// Role schemas
export const roleSchemas = {
  create: Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    permissions: Joi.object().optional(),
    isActive: Joi.boolean().default(true)
  }),

  update: Joi.object({
    name: Joi.string().min(2).max(100).optional(),
    description: Joi.string().max(500).optional(),
    permissions: Joi.object().optional(),
    isActive: Joi.boolean().optional()
  })
};
