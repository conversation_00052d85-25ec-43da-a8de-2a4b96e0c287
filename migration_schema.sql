-- PostgreSQL Migration Schema for Local Database
-- Modified from Supabase backup to work with local PostgreSQL

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- <PERSON><PERSON> custom types
CREATE TYPE public.department_type AS ENUM (
    'emergency',
    'surgery',
    'pediatrics',
    'cardiology',
    'orthopedics',
    'pharmacy',
    'laboratory',
    'radiology',
    'administration',
    'maintenance'
);

CREATE TYPE public.employee_role AS ENUM (
    'admin',
    'doctor',
    'nurse',
    'pharmacist',
    'technician',
    'receptionist',
    'manager'
);

CREATE TYPE public.employee_status AS ENUM (
    'active',
    'inactive',
    'on_leave',
    'terminated'
);

CREATE TYPE public.shift_status AS ENUM (
    'scheduled',
    'completed',
    'cancelled',
    'no_show'
);

CREATE TYPE public.shift_type AS ENUM (
    'morning',
    'afternoon',
    'night',
    'rotating',
    'regular'
);

-- Create users table to replace auth.users
CREATE TABLE public.users (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    email text UNIQUE NOT NULL,
    password_hash text NOT NULL,
    email_confirmed boolean DEFAULT false,
    suspended boolean DEFAULT false,
    failed_login_attempts integer DEFAULT 0,
    locked_until timestamp with time zone,
    last_login timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create profiles table
CREATE TABLE public.profiles (
    id uuid NOT NULL PRIMARY KEY,
    first_name text,
    last_name text,
    email text,
    role public.employee_role DEFAULT 'nurse'::public.employee_role,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    FOREIGN KEY (id) REFERENCES public.users(id) ON DELETE CASCADE
);

-- Create departments table
CREATE TABLE public.departments (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    name text NOT NULL,
    description text,
    manager_id uuid,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create roles table
CREATE TABLE public.roles (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    name text NOT NULL,
    description text,
    permissions jsonb,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create positions table
CREATE TABLE public.positions (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    name text NOT NULL,
    description text,
    department_id uuid,
    role_id uuid,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    FOREIGN KEY (department_id) REFERENCES public.departments(id),
    FOREIGN KEY (role_id) REFERENCES public.roles(id)
);

-- Create employees table
CREATE TABLE public.employees (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    employee_id text NOT NULL UNIQUE,
    first_name text NOT NULL,
    last_name text NOT NULL,
    email text NOT NULL UNIQUE,
    phone text,
    role public.employee_role NOT NULL,
    department public.department_type NOT NULL,
    "position" text NOT NULL,
    join_date date NOT NULL,
    status public.employee_status DEFAULT 'active'::public.employee_status NOT NULL,
    shift public.shift_type NOT NULL,
    salary numeric(12,2),
    avatar text,
    address text,
    user_id uuid,
    created_by uuid,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES public.users(id)
);

-- Create schedules table
CREATE TABLE public.schedules (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    employee_id uuid NOT NULL,
    shift_date date NOT NULL,
    shift_type public.shift_type NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone NOT NULL,
    status public.shift_status DEFAULT 'scheduled'::public.shift_status NOT NULL,
    notes text,
    created_by uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    FOREIGN KEY (employee_id) REFERENCES public.employees(id),
    FOREIGN KEY (created_by) REFERENCES public.users(id)
);

-- Create leave_requests table
CREATE TABLE public.leave_requests (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    employee_id uuid NOT NULL,
    leave_type text NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL,
    reason text,
    status text DEFAULT 'pending'::text NOT NULL,
    approved_by uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    FOREIGN KEY (employee_id) REFERENCES public.employees(id),
    FOREIGN KEY (approved_by) REFERENCES public.users(id)
);

-- Create password_resets table
CREATE TABLE public.password_resets (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    user_id uuid NOT NULL,
    token_hash text NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE,
    UNIQUE(user_id)
);

-- Create email_verifications table
CREATE TABLE public.email_verifications (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    user_id uuid NOT NULL,
    token_hash text NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE,
    UNIQUE(user_id)
);

-- Create user_activity_logs table
CREATE TABLE public.user_activity_logs (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    user_id uuid,
    action text NOT NULL,
    resource text,
    resource_id uuid,
    ip_address inet,
    user_agent text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL
);

-- Add foreign key for departments manager
ALTER TABLE public.departments 
ADD CONSTRAINT departments_manager_id_fkey 
FOREIGN KEY (manager_id) REFERENCES public.employees(id);

-- Create indexes for performance
CREATE INDEX idx_employees_email ON public.employees(email);
CREATE INDEX idx_employees_employee_id ON public.employees(employee_id);
CREATE INDEX idx_schedules_employee_id ON public.schedules(employee_id);
CREATE INDEX idx_schedules_shift_date ON public.schedules(shift_date);
CREATE INDEX idx_leave_requests_employee_id ON public.leave_requests(employee_id);
CREATE INDEX idx_profiles_email ON public.profiles(email);
CREATE INDEX idx_password_resets_user_id ON public.password_resets(user_id);
CREATE INDEX idx_password_resets_expires_at ON public.password_resets(expires_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON public.departments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_positions_updated_at BEFORE UPDATE ON public.positions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_employees_updated_at BEFORE UPDATE ON public.employees FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_schedules_updated_at BEFORE UPDATE ON public.schedules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_leave_requests_updated_at BEFORE UPDATE ON public.leave_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create cleanup function for expired password reset tokens
CREATE OR REPLACE FUNCTION cleanup_expired_password_resets()
RETURNS void AS $$
BEGIN
    DELETE FROM public.password_resets WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;
