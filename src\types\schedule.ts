
export interface Schedule {
  id: string;
  employeeId: string;
  shiftDate: string;
  shiftType: 'morning' | 'afternoon' | 'night' | 'rotating' | 'regular';
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  notes?: string;
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
  employee?: {
    firstName: string;
    lastName: string;
    department: string;
    position: string;
  };
}

export interface LeaveRequest {
  id: string;
  employeeId: string;
  startDate: string;
  endDate: string;
  leaveType: string;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  createdAt?: string;
  updatedAt?: string;
  employee?: {
    firstName: string;
    lastName: string;
    department: string;
    position: string;
  };
}
