# Supabase to PostgreSQL + Express.js Migration Guide

This document provides a complete guide for migrating your React application from Supabase to a local PostgreSQL database with an Express.js backend API.

## 🏗️ Architecture Overview

**Before (Supabase):**
```
React Frontend → Supabase Client → Supabase Cloud (PostgreSQL + Auth + API)
```

**After (Local PostgreSQL + Express.js):**
```
React Frontend → Axios HTTP Client → Express.js API → Local PostgreSQL Database
```

## 📁 Project Structure

```
project-root/
├── backend/                    # Express.js API server
│   ├── src/
│   │   ├── config/
│   │   │   └── database.ts     # PostgreSQL connection
│   │   ├── middleware/
│   │   │   ├── auth.ts         # JWT authentication
│   │   │   ├── validation.ts   # Request validation
│   │   │   └── index.ts        # Middleware configuration
│   │   ├── services/
│   │   │   ├── authService.ts  # Authentication logic
│   │   │   ├── employeeService.ts
│   │   │   ├── scheduleService.ts
│   │   │   ├── leaveRequestService.ts
│   │   │   └── managementService.ts
│   │   ├── routes/
│   │   │   ├── auth.ts         # Auth endpoints
│   │   │   ├── employees.ts    # Employee CRUD
│   │   │   ├── schedules.ts    # Schedule CRUD
│   │   │   ├── leaveRequests.ts
│   │   │   └── management.ts   # Departments, roles, positions
│   │   └── server.ts           # Express app entry point
│   ├── package.json
│   ├── tsconfig.json
│   └── .env.example
├── src/                        # React frontend (updated)
│   ├── lib/
│   │   └── api.ts              # API client (replaces Supabase client)
│   ├── hooks/
│   │   ├── useAuthNew.tsx      # Updated auth hook
│   │   ├── useEmployeesNew.ts  # Updated employee hook
│   │   ├── useSchedulesNew.ts  # Updated schedule hook
│   │   ├── useLeaveRequestsNew.ts
│   │   └── useManagementNew.ts
│   └── ...
├── migration_schema.sql        # Database schema for PostgreSQL
├── migrate_data.sql           # Data migration script
└── migration_steps.md         # Step-by-step migration guide
```

## 🚀 Quick Start

### 1. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit .env with your database credentials
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=manajemen_karyawan
# DB_USER=app_user
# DB_PASSWORD=your_password
# JWT_SECRET=your-super-secret-jwt-key

# Build and start the server
npm run build
npm start

# For development
npm run dev
```

### 2. Database Setup

```bash
# Create PostgreSQL database
createdb manajemen_karyawan

# Run schema migration
psql -U postgres -d manajemen_karyawan -f migration_schema.sql

# Import your data (after extracting from Supabase backup)
psql -U app_user -d manajemen_karyawan -f migrate_data.sql
```

### 3. Frontend Setup

```bash
# Install new dependencies (remove Supabase, add Axios)
npm uninstall @supabase/supabase-js
npm install axios

# Copy environment file
cp .env.example .env

# Edit .env
# VITE_API_BASE_URL=http://localhost:3001

# Update imports in your components
# Replace: import { useAuth } from '@/hooks/useAuth'
# With: import { useAuth } from '@/hooks/useAuthNew'

# Start development server
npm run dev
```

## 🔧 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/refresh` - Refresh access token
- `GET /api/v1/auth/me` - Get current user
- `PUT /api/v1/auth/profile` - Update user profile
- `PUT /api/v1/auth/change-password` - Change password
- `GET /api/v1/auth/verify` - Verify token

### Employees
- `GET /api/v1/employees` - Get all employees
- `GET /api/v1/employees/:id` - Get employee by ID
- `POST /api/v1/employees` - Create employee
- `PUT /api/v1/employees/:id` - Update employee
- `DELETE /api/v1/employees/:id` - Delete employee
- `GET /api/v1/employees/statistics` - Get employee statistics

### Schedules
- `GET /api/v1/schedules` - Get all schedules
- `GET /api/v1/schedules/:id` - Get schedule by ID
- `POST /api/v1/schedules` - Create schedule
- `PUT /api/v1/schedules/:id` - Update schedule
- `DELETE /api/v1/schedules/:id` - Delete schedule
- `GET /api/v1/schedules/statistics` - Get schedule statistics

### Leave Requests
- `GET /api/v1/leave-requests` - Get all leave requests
- `GET /api/v1/leave-requests/:id` - Get leave request by ID
- `POST /api/v1/leave-requests` - Create leave request
- `PUT /api/v1/leave-requests/:id` - Update leave request
- `DELETE /api/v1/leave-requests/:id` - Delete leave request
- `PATCH /api/v1/leave-requests/:id/approve` - Approve leave request
- `PATCH /api/v1/leave-requests/:id/reject` - Reject leave request
- `GET /api/v1/leave-requests/statistics` - Get leave request statistics

### Management
- `GET /api/v1/management/departments` - Get all departments
- `POST /api/v1/management/departments` - Create department
- `PUT /api/v1/management/departments/:id` - Update department
- `DELETE /api/v1/management/departments/:id` - Delete department
- `GET /api/v1/management/roles` - Get all roles
- `POST /api/v1/management/roles` - Create role
- `PUT /api/v1/management/roles/:id` - Update role
- `GET /api/v1/management/positions` - Get all positions
- `POST /api/v1/management/positions` - Create position
- `PUT /api/v1/management/positions/:id` - Update position

## 🔐 Authentication Flow

1. **Login**: User provides email/password → API validates → Returns JWT access token + sets refresh token cookie
2. **API Requests**: Frontend sends JWT in Authorization header → Backend validates → Processes request
3. **Token Refresh**: When access token expires → Frontend automatically requests new token using refresh cookie
4. **Logout**: Frontend calls logout endpoint → Backend clears refresh cookie → Frontend removes access token

## 🛡️ Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcrypt with configurable rounds
- **Rate Limiting**: Prevents brute force attacks
- **CORS Protection**: Configurable allowed origins
- **Input Validation**: Joi schema validation
- **SQL Injection Prevention**: Parameterized queries
- **Role-Based Access Control**: Admin, Manager, Employee permissions

## 📊 Key Differences from Supabase

| Feature | Supabase | Express.js + PostgreSQL |
|---------|----------|-------------------------|
| **Authentication** | Built-in Auth | Custom JWT implementation |
| **Database Access** | Direct from frontend | API endpoints only |
| **Real-time** | Built-in subscriptions | Custom WebSocket implementation needed |
| **File Storage** | Built-in storage | Custom file handling needed |
| **Row Level Security** | Automatic | Custom middleware authorization |
| **API Generation** | Automatic | Manual endpoint creation |
| **Hosting** | Cloud-hosted | Self-hosted |

## 🧪 Testing

```bash
# Backend tests
cd backend
npm test

# Test API endpoints
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Frontend tests
npm test
```

## 🚀 Deployment

### Backend Deployment
1. Set production environment variables
2. Build the application: `npm run build`
3. Start with PM2: `pm2 start dist/server.js`
4. Configure reverse proxy (Nginx)
5. Set up SSL certificates

### Database Deployment
1. Set up PostgreSQL server
2. Configure connection pooling
3. Set up automated backups
4. Configure monitoring

### Frontend Deployment
1. Update API base URL for production
2. Build: `npm run build`
3. Deploy to static hosting (Vercel, Netlify, etc.)

## 🔧 Troubleshooting

### Common Issues

1. **CORS Errors**: Check `ALLOWED_ORIGINS` in backend .env
2. **Database Connection**: Verify PostgreSQL is running and credentials are correct
3. **Token Errors**: Check JWT_SECRET is set and consistent
4. **Port Conflicts**: Ensure backend (3001) and frontend (8080) ports are available

### Debug Commands

```bash
# Check database connection
psql -U app_user -d manajemen_karyawan -c "SELECT version();"

# Check backend logs
npm run dev # Shows detailed logs in development

# Test API endpoints
curl -X GET http://localhost:3001/health
```

## 📈 Performance Optimization

1. **Database Indexing**: Add indexes for frequently queried columns
2. **Connection Pooling**: Configure PostgreSQL connection pool
3. **Caching**: Implement Redis for session storage
4. **Compression**: Enable gzip compression
5. **Rate Limiting**: Configure appropriate limits

## 🔄 Migration Checklist

- [ ] PostgreSQL database set up and running
- [ ] Backend API deployed and tested
- [ ] Database schema migrated
- [ ] Data imported from Supabase backup
- [ ] Frontend updated to use new API
- [ ] Authentication flow tested
- [ ] All CRUD operations tested
- [ ] Role-based access control verified
- [ ] Error handling implemented
- [ ] Production environment configured

## 📞 Support

For issues or questions about this migration:
1. Check the troubleshooting section
2. Review API documentation
3. Test with provided curl commands
4. Check server logs for detailed error messages
