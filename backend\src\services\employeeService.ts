// Employee service
import { query, transaction, Employee } from '@/config/database';

export class EmployeeService {
  // Get all employees
  static async getAll(): Promise<Employee[]> {
    try {
      const result = await query(`
        SELECT * FROM employees 
        ORDER BY created_at DESC
      `);
      return result.rows;
    } catch (error) {
      console.error('Get all employees error:', error);
      throw new Error('Failed to fetch employees');
    }
  }

  // Get employee by ID
  static async getById(id: string): Promise<Employee | null> {
    try {
      const result = await query(
        'SELECT * FROM employees WHERE id = $1',
        [id]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Get employee by ID error:', error);
      throw new Error('Failed to fetch employee');
    }
  }

  // Get employee by employee ID
  static async getByEmployeeId(employeeId: string): Promise<Employee | null> {
    try {
      const result = await query(
        'SELECT * FROM employees WHERE employee_id = $1',
        [employeeId]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Get employee by employee ID error:', error);
      throw new Error('Failed to fetch employee');
    }
  }

  // Get employees by department
  static async getByDepartment(department: string): Promise<Employee[]> {
    try {
      const result = await query(
        'SELECT * FROM employees WHERE department = $1 ORDER BY created_at DESC',
        [department]
      );
      return result.rows;
    } catch (error) {
      console.error('Get employees by department error:', error);
      throw new Error('Failed to fetch employees by department');
    }
  }

  // Get employees by role
  static async getByRole(role: string): Promise<Employee[]> {
    try {
      const result = await query(
        'SELECT * FROM employees WHERE role = $1 ORDER BY created_at DESC',
        [role]
      );
      return result.rows;
    } catch (error) {
      console.error('Get employees by role error:', error);
      throw new Error('Failed to fetch employees by role');
    }
  }

  // Create employee
  static async create(employeeData: Omit<Employee, 'id' | 'created_at' | 'updated_at'>, createdBy: string): Promise<Employee> {
    try {
      // Check if employee ID already exists
      const existingEmployee = await this.getByEmployeeId(employeeData.employee_id);
      if (existingEmployee) {
        throw new Error('Employee ID already exists');
      }

      // Check if email already exists
      const emailCheck = await query(
        'SELECT id FROM employees WHERE email = $1',
        [employeeData.email]
      );
      if (emailCheck.rows.length > 0) {
        throw new Error('Email already exists');
      }

      const result = await query(`
        INSERT INTO employees (
          employee_id, first_name, last_name, email, phone, role, 
          department, position, join_date, status, shift, salary, 
          avatar, address, user_id, created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
        RETURNING *
      `, [
        employeeData.employee_id,
        employeeData.first_name,
        employeeData.last_name,
        employeeData.email,
        employeeData.phone,
        employeeData.role,
        employeeData.department,
        employeeData.position,
        employeeData.join_date,
        employeeData.status || 'active',
        employeeData.shift,
        employeeData.salary,
        employeeData.avatar,
        employeeData.address,
        employeeData.user_id,
        createdBy
      ]);

      return result.rows[0];
    } catch (error) {
      console.error('Create employee error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create employee');
    }
  }

  // Update employee
static async update(id: string, employeeData: Partial<Employee>): Promise<Employee> {
  try {
    // Remove fields that shouldn't be updated
    const { id: _, created_at, updated_at, ...updateData } = employeeData;
    
    const fields = Object.keys(updateData);
    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    // Check if employee exists
    const existingEmployee = await this.getById(id);
    if (!existingEmployee) {
      throw new Error('Employee not found');
    }

    // Check if employee_id is being updated and if it already exists
    if (updateData.employee_id && updateData.employee_id !== existingEmployee.employee_id) {
      const existingEmployeeId = await this.getByEmployeeId(updateData.employee_id);
      if (existingEmployeeId) {
        throw new Error('Employee ID already exists');
      }
    }

    // Check if email is being updated and if it already exists
    if (updateData.email && updateData.email !== existingEmployee.email) {
      const emailCheck = await query(
        'SELECT id FROM employees WHERE email = $1 AND id != $2',
        [updateData.email, id]
      );
      if (emailCheck.rows.length > 0) {
        throw new Error('Email already exists');
      }
    }

    // FIX: Use keyof typeof updateData instead of keyof Employee
    const values = fields.map(field => updateData[field as keyof typeof updateData]);
    const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

    const result = await query(`
      UPDATE employees 
      SET ${setClause}, updated_at = NOW()
      WHERE id = $1
      RETURNING *
    `, [id, ...values]);

    if (result.rows.length === 0) {
      throw new Error('Employee not found');
    }

    return result.rows[0];
  } catch (error) {
    console.error('Update employee error:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to update employee');
  }
}

  // Delete employee
  static async delete(id: string): Promise<void> {
    try {
      // Check if employee exists
      const existingEmployee = await this.getById(id);
      if (!existingEmployee) {
        throw new Error('Employee not found');
      }

      // Check if employee has related records (schedules, leave requests)
      const scheduleCheck = await query(
        'SELECT COUNT(*) FROM schedules WHERE employee_id = $1',
        [id]
      );
      
      const leaveRequestCheck = await query(
        'SELECT COUNT(*) FROM leave_requests WHERE employee_id = $1',
        [id]
      );

      if (parseInt(scheduleCheck.rows[0].count) > 0 || parseInt(leaveRequestCheck.rows[0].count) > 0) {
        // Instead of deleting, mark as terminated
        await this.update(id, { status: 'terminated' });
        return;
      }

      // Safe to delete if no related records
      await query('DELETE FROM employees WHERE id = $1', [id]);
    } catch (error) {
      console.error('Delete employee error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete employee');
    }
  }

  // Search employees
  static async search(searchTerm: string): Promise<Employee[]> {
    try {
      const result = await query(`
        SELECT * FROM employees 
        WHERE 
          first_name ILIKE $1 OR 
          last_name ILIKE $1 OR 
          email ILIKE $1 OR 
          employee_id ILIKE $1 OR
          position ILIKE $1
        ORDER BY created_at DESC
      `, [`%${searchTerm}%`]);

      return result.rows;
    } catch (error) {
      console.error('Search employees error:', error);
      throw new Error('Failed to search employees');
    }
  }

  // Get employee statistics
  static async getStatistics(): Promise<any> {
    try {
      const totalResult = await query('SELECT COUNT(*) as total FROM employees');
      const activeResult = await query('SELECT COUNT(*) as active FROM employees WHERE status = $1', ['active']);
      const departmentResult = await query(`
        SELECT department, COUNT(*) as count 
        FROM employees 
        GROUP BY department 
        ORDER BY count DESC
      `);
      const roleResult = await query(`
        SELECT role, COUNT(*) as count 
        FROM employees 
        GROUP BY role 
        ORDER BY count DESC
      `);

      return {
        total: parseInt(totalResult.rows[0].total),
        active: parseInt(activeResult.rows[0].active),
        byDepartment: departmentResult.rows,
        byRole: roleResult.rows
      };
    } catch (error) {
      console.error('Get employee statistics error:', error);
      throw new Error('Failed to get employee statistics');
    }
  }
}
