
import { useState } from 'react';
import { Employee } from '@/types/employee';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Briefcase,
  GraduationCap,
  Award,
  Users,
  FileText,
  Edit,
  Upload
} from 'lucide-react';

interface EmployeeDetailProps {
  employee: Employee;
  onEdit?: () => void;
  onUploadPhoto?: () => void;
}

export function EmployeeDetail({ employee, onEdit, onUploadPhoto }: EmployeeDetailProps) {
  const [activeTab, setActiveTab] = useState('profile');

  const getRoleBadgeColor = (role: string) => {
    const colors = {
      doctor: 'bg-blue-100 text-blue-800',
      nurse: 'bg-green-100 text-green-800',
      pharmacist: 'bg-purple-100 text-purple-800',
      technician: 'bg-orange-100 text-orange-800',
      admin: 'bg-gray-100 text-gray-800',
      receptionist: 'bg-pink-100 text-pink-800',
      manager: 'bg-indigo-100 text-indigo-800',
    };
    return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getStatusBadgeColor = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      on_leave: 'bg-yellow-100 text-yellow-800',
      terminated: 'bg-red-100 text-red-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header with Photo and Basic Info */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-6">
              <div className="relative">
                <Avatar className="w-24 h-24">
                  <AvatarImage src={employee.avatar} alt={`${employee.firstName} ${employee.lastName}`} />
                  <AvatarFallback className="text-xl">
                    {employee.firstName[0]}{employee.lastName[0]}
                  </AvatarFallback>
                </Avatar>
                <Button
                  size="sm"
                  variant="outline"
                  className="absolute -bottom-2 -right-2 rounded-full p-2"
                  onClick={onUploadPhoto}
                >
                  <Upload className="w-3 h-3" />
                </Button>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {employee.firstName} {employee.lastName}
                </h1>
                <p className="text-lg text-gray-600">{employee.position}</p>
                <p className="text-sm text-gray-500">ID: {employee.employeeId}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <Badge className={getRoleBadgeColor(employee.role)}>
                    {employee.role}
                  </Badge>
                  <Badge className={getStatusBadgeColor(employee.status)}>
                    {employee.status}
                  </Badge>
                </div>
              </div>
            </div>
            <Button onClick={onEdit} className="flex items-center space-x-2">
              <Edit className="w-4 h-4" />
              <span>Edit Profile</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Information Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile">Profil</TabsTrigger>
          <TabsTrigger value="education">Pendidikan</TabsTrigger>
          <TabsTrigger value="certifications">Sertifikasi</TabsTrigger>
          <TabsTrigger value="emergency">Kontak Darurat</TabsTrigger>
          <TabsTrigger value="documents">Dokumen</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="w-5 h-5" />
                  <span>Informasi Personal</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="w-4 h-4 text-gray-500" />
                  <span>{employee.email}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-gray-500" />
                  <span>{employee.phone || 'Tidak tersedia'}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  <span>{employee.address || 'Alamat belum diisi'}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <span>Bergabung: {new Date(employee.joinDate).toLocaleDateString('id-ID')}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Briefcase className="w-5 h-5" />
                  <span>Informasi Pekerjaan</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Departemen</label>
                  <p className="capitalize">{employee.department}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Shift</label>
                  <p className="capitalize">{employee.shift}</p>
                </div>
                {employee.salary && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Gaji</label>
                    <p>Rp {employee.salary.toLocaleString('id-ID')}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-gray-500">Keahlian</label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {employee.skills?.map((skill, index) => (
                      <Badge key={index} variant="outline">{skill}</Badge>
                    )) || <span className="text-gray-400">Belum ada data</span>}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="education" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <GraduationCap className="w-5 h-5" />
                <span>Riwayat Pendidikan</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <GraduationCap className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Data pendidikan akan segera ditambahkan</p>
                <Button variant="outline" className="mt-4">
                  Tambah Riwayat Pendidikan
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="certifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Award className="w-5 h-5" />
                <span>Sertifikasi dan Lisensi</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {employee.certifications && employee.certifications.length > 0 ? (
                <div className="space-y-4">
                  {employee.certifications.map((cert, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{cert}</h4>
                          <p className="text-sm text-gray-500">Status: Aktif</p>
                        </div>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          Valid
                        </Badge>
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full">
                    Tambah Sertifikasi Baru
                  </Button>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Award className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Belum ada sertifikasi yang terdaftar</p>
                  <Button variant="outline" className="mt-4">
                    Tambah Sertifikasi
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="emergency" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="w-5 h-5" />
                <span>Kontak Darurat</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {employee.emergencyContact ? (
                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium">{employee.emergencyContact.name}</h4>
                    <p className="text-sm text-gray-600">{employee.emergencyContact.relationship}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      <Phone className="w-4 h-4 text-gray-500" />
                      <span>{employee.emergencyContact.phone}</span>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full">
                    Edit Kontak Darurat
                  </Button>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Kontak darurat belum diisi</p>
                  <Button variant="outline" className="mt-4">
                    Tambah Kontak Darurat
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>Dokumen</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Fitur upload dokumen akan segera tersedia</p>
                <Button variant="outline" className="mt-4">
                  Upload Dokumen
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
