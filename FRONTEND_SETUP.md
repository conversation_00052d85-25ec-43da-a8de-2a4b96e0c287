# Frontend Setup Guide

This guide will help you complete the frontend migration from Supabase to the Express.js backend.

## ✅ What's Already Done

The frontend has been updated with:
- ✅ New API client (`src/lib/api.ts`) using Axios
- ✅ Updated authentication hook (`src/hooks/useAuthNew.tsx`)
- ✅ Updated data hooks for employees, schedules, leave requests, and management
- ✅ All components updated to use new hooks
- ✅ Data transformation between frontend (camelCase) and backend (snake_case)
- ✅ Old Supabase hooks removed
- ✅ JWT token management with automatic refresh

## 🚀 Quick Setup Steps

### 1. Install Dependencies

```bash
# Remove Supabase (if not already done)
npm uninstall @supabase/supabase-js

# Install Axios (if not already done)
npm install axios
```

### 2. Environment Configuration

```bash
# Copy environment file
cp .env.example .env

# The .env file should contain:
# VITE_API_BASE_URL=http://localhost:3001
```

### 3. Start the Application

```bash
# Make sure backend is running first
cd backend
npm run dev

# In another terminal, start frontend
cd ..
npm run dev
```

## 🔧 Key Changes Made

### Authentication
- **Old**: Direct Supabase auth with `supabase.auth.signIn()`
- **New**: API-based auth with JWT tokens stored in localStorage
- **Auto-refresh**: Tokens automatically refresh when expired

### Data Fetching
- **Old**: Direct Supabase queries with `supabase.from('table').select()`
- **New**: HTTP API calls with `employeeApi.getAll()`, etc.
- **Transformation**: Automatic conversion between camelCase (frontend) and snake_case (backend)

### Hooks Updated
- `useAuth` → `useAuthNew`
- `useEmployees` → `useEmployeesNew`
- `useSchedules` → `useSchedulesNew`
- `useLeaveRequests` → `useLeaveRequestsNew`
- `useManagement` → `useManagementNew`

## 📊 Data Flow

```
React Component
    ↓
New Hook (e.g., useEmployeesNew)
    ↓
API Client (src/lib/api.ts)
    ↓
HTTP Request with JWT
    ↓
Express.js Backend
    ↓
PostgreSQL Database
```

## 🔐 Authentication Flow

1. **Login**: User enters credentials → API validates → Returns JWT + refresh token
2. **API Calls**: Frontend sends JWT in Authorization header
3. **Auto-refresh**: When JWT expires, automatically gets new token
4. **Logout**: Clears tokens and redirects to login

## 🧪 Testing the Migration

### 1. Test Authentication
- Go to `/auth` page
- Try logging in with test credentials
- Check that JWT token is stored in localStorage
- Verify user profile is displayed correctly

### 2. Test Employee Management
- Navigate to `/employees`
- Try creating, editing, and deleting employees
- Check that data transformations work correctly
- Verify role-based access control

### 3. Test Schedule Management
- Go to `/schedule`
- Create new schedules
- Test leave request functionality
- Check calendar views

### 4. Test Management Features
- Navigate to `/management`
- Test department, role, and position management
- Verify admin-only access

## 🐛 Troubleshooting

### Common Issues

1. **"Network Error" or API not responding**
   - Check that backend is running on port 3001
   - Verify VITE_API_BASE_URL in .env file
   - Check browser console for CORS errors

2. **Authentication not working**
   - Check JWT_SECRET is set in backend .env
   - Verify database connection
   - Check browser localStorage for auth_token

3. **Data not displaying correctly**
   - Check browser console for transformation errors
   - Verify API endpoints are returning expected data format
   - Check network tab for API response structure

4. **Role-based access not working**
   - Verify user profile has correct role in database
   - Check JWT token contains user information
   - Verify middleware is working in backend

### Debug Commands

```bash
# Check if backend is running
curl http://localhost:3001/health

# Test API endpoint
curl http://localhost:3001/api/v1/employees \
  -H "Authorization: Bearer YOUR_TOKEN"

# Check frontend build
npm run build

# Check for TypeScript errors
npx tsc --noEmit
```

## 📝 Environment Variables

### Frontend (.env)
```env
VITE_API_BASE_URL=http://localhost:3001
VITE_NODE_ENV=development
```

### Backend (.env)
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=manajemen_karyawan
DB_USER=app_user
DB_PASSWORD=your_password
JWT_SECRET=your-super-secret-jwt-key
NODE_ENV=development
PORT=3001
```

## 🚀 Production Deployment

### Frontend
1. Update VITE_API_BASE_URL to production backend URL
2. Build: `npm run build`
3. Deploy dist folder to static hosting (Vercel, Netlify, etc.)

### Backend
1. Set production environment variables
2. Build: `cd backend && npm run build`
3. Start: `npm start`
4. Use process manager like PM2 for production

## ✅ Migration Checklist

- [ ] Backend API running and accessible
- [ ] Frontend environment variables configured
- [ ] Authentication working (login/logout)
- [ ] Employee CRUD operations working
- [ ] Schedule management working
- [ ] Leave request functionality working
- [ ] Management features working
- [ ] Role-based access control working
- [ ] Data transformations working correctly
- [ ] Error handling working properly
- [ ] No console errors in browser
- [ ] All old Supabase references removed

## 🎉 Success!

If all items in the checklist are complete, your migration from Supabase to Express.js + PostgreSQL is successful! 

Your application now has:
- Full control over the backend
- Custom authentication with JWT
- Optimized database queries
- Role-based security
- Scalable architecture

For any issues, refer to the API_TESTING.md file for detailed testing instructions.
