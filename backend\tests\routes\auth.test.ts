// Auth Routes Tests
import request from 'supertest';
import express from 'express';
import authRoutes from '../../src/routes/auth';
import { TEST_CONSTANTS } from '../setup';

// Mock the auth service
jest.mock('../../src/services/authService');
import { AuthService } from '../../src/services/authService';
const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;

// Mock middleware
jest.mock('../../src/middleware/validation', () => ({
  validate: () => (req: any, res: any, next: any) => next(),
  authSchemas: {
    register: {},
    login: {},
    changePassword: {},
    forgotPassword: {},
    resetPassword: {},
  },
  profileSchemas: {
    update: {},
  },
}));

jest.mock('../../src/middleware/auth', () => ({
  authenticateToken: (req: any, res: any, next: any) => {
    req.user = { id: 'test-user-id', email: '<EMAIL>', role: 'nurse' };
    next();
  },
  verifyRefreshToken: jest.fn(),
  generateToken: jest.fn().mockReturnValue('new-access-token'),
}));

const app = express();
app.use(express.json());
app.use('/auth', authRoutes);

describe('Auth Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /auth/register', () => {
    it('should register user successfully', async () => {
      const userData = {
        email: TEST_CONSTANTS.VALID_EMAIL,
        password: TEST_CONSTANTS.VALID_PASSWORD,
        firstName: 'John',
        lastName: 'Doe',
        role: 'nurse'
      };

      mockAuthService.register.mockResolvedValueOnce({
        user: {
          id: 'user-id',
          email: userData.email,
          email_confirmed: false,
          profile: {
            id: 'user-id',
            first_name: userData.firstName,
            last_name: userData.lastName,
            email: userData.email,
            role: userData.role,
            created_at: new Date(),
            updated_at: new Date(),
          }
        }
      });

      const response = await request(app)
        .post('/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.message).toBe('User registered successfully');
      expect(response.body.user.email).toBe(userData.email);
      expect(mockAuthService.register).toHaveBeenCalledWith(
        userData.email,
        userData.password,
        userData.firstName,
        userData.lastName,
        userData.role
      );
    });

    it('should return error for existing email', async () => {
      const userData = {
        email: TEST_CONSTANTS.VALID_EMAIL,
        password: TEST_CONSTANTS.VALID_PASSWORD,
      };

      mockAuthService.register.mockResolvedValueOnce({
        error: 'User with this email already exists'
      });

      const response = await request(app)
        .post('/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.error).toBe('User with this email already exists');
    });
  });

  describe('POST /auth/login', () => {
    it('should login user successfully', async () => {
      const loginData = {
        email: TEST_CONSTANTS.VALID_EMAIL,
        password: TEST_CONSTANTS.VALID_PASSWORD,
      };

      const mockAuthResponse = {
        user: {
          id: 'user-id',
          email: loginData.email,
          email_confirmed: true,
          profile: {
            id: 'user-id',
            first_name: 'John',
            last_name: 'Doe',
            email: loginData.email,
            role: 'nurse',
            created_at: new Date(),
            updated_at: new Date(),
          }
        },
        access_token: 'access-token',
        refresh_token: 'refresh-token',
        expires_in: 604800
      };

      mockAuthService.login.mockResolvedValueOnce({
        auth: mockAuthResponse
      });

      const response = await request(app)
        .post('/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.message).toBe('Login successful');
      expect(response.body.user.email).toBe(loginData.email);
      expect(response.body.access_token).toBe('access-token');
      expect(response.headers['set-cookie']).toBeDefined();
    });

    it('should return error for invalid credentials', async () => {
      const loginData = {
        email: TEST_CONSTANTS.VALID_EMAIL,
        password: 'wrongpassword',
      };

      mockAuthService.login.mockResolvedValueOnce({
        error: 'Invalid email or password'
      });

      const response = await request(app)
        .post('/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.error).toBe('Invalid email or password');
    });
  });

  describe('POST /auth/logout', () => {
    it('should logout user successfully', async () => {
      const response = await request(app)
        .post('/auth/logout')
        .expect(200);

      expect(response.body.message).toBe('Logged out successfully');
      expect(response.headers['set-cookie']).toBeDefined();
    });
  });

  describe('GET /auth/me', () => {
    it('should get current user successfully', async () => {
      const mockUser = {
        id: 'test-user-id',
        email: '<EMAIL>',
        email_confirmed: true,
        profile: {
          id: 'test-user-id',
          first_name: 'Test',
          last_name: 'User',
          email: '<EMAIL>',
          role: 'nurse',
          created_at: new Date(),
          updated_at: new Date(),
        }
      };

      mockAuthService.getUserById.mockResolvedValueOnce(mockUser);

      const response = await request(app)
        .get('/auth/me')
        .expect(200);

      expect(response.body.user.email).toBe(mockUser.email);
    });

    it('should return error for non-existent user', async () => {
      mockAuthService.getUserById.mockResolvedValueOnce(null);

      const response = await request(app)
        .get('/auth/me')
        .expect(404);

      expect(response.body.error).toBe('User not found');
    });
  });

  describe('PUT /auth/profile', () => {
    it('should update profile successfully', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
        email: '<EMAIL>'
      };

      const mockProfile = {
        id: 'test-user-id',
        first_name: updateData.firstName,
        last_name: updateData.lastName,
        email: updateData.email,
        role: 'nurse',
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockAuthService.updateProfile.mockResolvedValueOnce({
        profile: mockProfile
      });

      const response = await request(app)
        .put('/auth/profile')
        .send(updateData)
        .expect(200);

      expect(response.body.message).toBe('Profile updated successfully');
      expect(response.body.profile.first_name).toBe(updateData.firstName);
    });

    it('should return error for profile update failure', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name'
      };

      mockAuthService.updateProfile.mockResolvedValueOnce({
        error: 'Profile not found'
      });

      const response = await request(app)
        .put('/auth/profile')
        .send(updateData)
        .expect(400);

      expect(response.body.error).toBe('Profile not found');
    });
  });

  describe('PUT /auth/change-password', () => {
    it('should change password successfully', async () => {
      const passwordData = {
        currentPassword: 'oldpassword',
        newPassword: TEST_CONSTANTS.VALID_PASSWORD
      };

      mockAuthService.changePassword.mockResolvedValueOnce({
        success: true
      });

      const response = await request(app)
        .put('/auth/change-password')
        .send(passwordData)
        .expect(200);

      expect(response.body.message).toBe('Password changed successfully');
    });

    it('should return error for incorrect current password', async () => {
      const passwordData = {
        currentPassword: 'wrongpassword',
        newPassword: TEST_CONSTANTS.VALID_PASSWORD
      };

      mockAuthService.changePassword.mockResolvedValueOnce({
        error: 'Current password is incorrect'
      });

      const response = await request(app)
        .put('/auth/change-password')
        .send(passwordData)
        .expect(400);

      expect(response.body.error).toBe('Current password is incorrect');
    });
  });

  describe('GET /auth/verify', () => {
    it('should verify token successfully', async () => {
      const response = await request(app)
        .get('/auth/verify')
        .expect(200);

      expect(response.body.valid).toBe(true);
      expect(response.body.user.id).toBe('test-user-id');
      expect(response.body.user.email).toBe('<EMAIL>');
    });
  });
});
