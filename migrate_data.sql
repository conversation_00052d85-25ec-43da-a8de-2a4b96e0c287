-- Data Migration Script
-- Extract and migrate data from Supabase backup to local PostgreSQL

-- Note: This script assumes you have extracted the INSERT statements from your backup file
-- You'll need to modify the auth.users references to use the new users table

-- Example of how to migrate users data (you'll need to extract actual data from backup)
-- INSERT INTO public.users (id, email, password_hash, email_confirmed, created_at, updated_at)
-- VALUES 
-- ('existing-uuid-1', '<EMAIL>', 'hashed_password', true, now(), now()),
-- ('existing-uuid-2', '<EMAIL>', 'hashed_password', true, now(), now());

-- Migrate profiles data (extract from backup and modify)
-- INSERT INTO public.profiles (id, first_name, last_name, email, role, created_at, updated_at)
-- VALUES 
-- ('existing-uuid-1', 'Admin', 'User', '<EMAIL>', 'admin', now(), now());

-- Migrate departments data
-- INSERT INTO public.departments (id, name, description, manager_id, is_active, created_at, updated_at)
-- VALUES ...

-- Migrate roles data
-- INSERT INTO public.roles (id, name, description, permissions, is_active, created_at, updated_at)
-- VALUES ...

-- Migrate positions data
-- INSERT INTO public.positions (id, name, description, department_id, role_id, is_active, created_at, updated_at)
-- VALUES ...

-- Migrate employees data
-- INSERT INTO public.employees (id, employee_id, first_name, last_name, email, phone, role, department, position, join_date, status, shift, salary, avatar, address, user_id, created_by, created_at, updated_at)
-- VALUES ...

-- Migrate schedules data
-- INSERT INTO public.schedules (id, employee_id, shift_date, shift_type, start_time, end_time, status, notes, created_by, created_at, updated_at)
-- VALUES ...

-- Migrate leave_requests data
-- INSERT INTO public.leave_requests (id, employee_id, leave_type, start_date, end_date, reason, status, approved_by, created_at, updated_at)
-- VALUES ...

-- Create a default admin user for testing
INSERT INTO public.users (id, email, password_hash, email_confirmed, created_at, updated_at)
VALUES 
('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', '$2b$10$example.hash.here', true, now(), now())
ON CONFLICT (email) DO NOTHING;

INSERT INTO public.profiles (id, first_name, last_name, email, role, created_at, updated_at)
VALUES 
('550e8400-e29b-41d4-a716-446655440000', 'System', 'Administrator', '<EMAIL>', 'admin', now(), now())
ON CONFLICT (id) DO NOTHING;
