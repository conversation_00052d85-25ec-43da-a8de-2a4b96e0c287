// Auth Service Tests
import { AuthService } from '../../src/services/authService';
import { testPool, createTestUser, TEST_CONSTANTS } from '../setup';

// Mock the database module
jest.mock('../../src/config/database', () => ({
  query: jest.fn(),
  transaction: jest.fn(),
}));

// Mock the cache service
jest.mock('../../src/services/cacheService', () => ({
  cacheService: {
    getOrSet: jest.fn(),
    delete: jest.fn(),
    invalidatePattern: jest.fn(),
  },
  CacheKeys: {
    user: jest.fn(),
  },
  CacheTTL: {
    MEDIUM: 300000,
  },
  invalidateUserCache: jest.fn(),
}));

import { query, transaction } from '../../src/config/database';
const mockQuery = query as jest.MockedFunction<typeof query>;
const mockTransaction = transaction as jest.MockedFunction<typeof transaction>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('hashPassword', () => {
    it('should hash password correctly', async () => {
      const password = 'testpassword123';
      const hash = await AuthService.hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(50);
    });
  });

  describe('verifyPassword', () => {
    it('should verify correct password', async () => {
      const password = 'testpassword123';
      const hash = await AuthService.hashPassword(password);
      
      const isValid = await AuthService.verifyPassword(password, hash);
      expect(isValid).toBe(true);
    });

    it('should reject incorrect password', async () => {
      const password = 'testpassword123';
      const wrongPassword = 'wrongpassword';
      const hash = await AuthService.hashPassword(password);
      
      const isValid = await AuthService.verifyPassword(wrongPassword, hash);
      expect(isValid).toBe(false);
    });
  });

  describe('register', () => {
    it('should register new user successfully', async () => {
      const userData = {
        email: TEST_CONSTANTS.VALID_EMAIL,
        password: TEST_CONSTANTS.VALID_PASSWORD,
        firstName: 'John',
        lastName: 'Doe',
        role: 'nurse'
      };

      // Mock database responses
      mockQuery
        .mockResolvedValueOnce({ rows: [] }) // Check existing user
        .mockResolvedValueOnce({ rows: [{ id: 'user-id' }] }); // Insert user

      mockTransaction.mockImplementation(async (callback) => {
        const mockClient = {
          query: jest.fn()
            .mockResolvedValueOnce({ rows: [{ id: 'user-id', email: userData.email }] })
            .mockResolvedValueOnce({ rows: [] })
        };
        return callback(mockClient as any);
      });

      const result = await AuthService.register(
        userData.email,
        userData.password,
        userData.firstName,
        userData.lastName,
        userData.role
      );

      expect(result.error).toBeUndefined();
      expect(result.user).toBeDefined();
      expect(result.user?.email).toBe(userData.email);
    });

    it('should reject registration with existing email', async () => {
      const userData = {
        email: TEST_CONSTANTS.VALID_EMAIL,
        password: TEST_CONSTANTS.VALID_PASSWORD,
      };

      // Mock existing user
      mockQuery.mockResolvedValueOnce({ 
        rows: [{ id: 'existing-user-id' }] 
      });

      const result = await AuthService.register(userData.email, userData.password);

      expect(result.error).toBe('User with this email already exists');
      expect(result.user).toBeUndefined();
    });
  });

  describe('login', () => {
    it('should login user successfully', async () => {
      const userData = {
        email: TEST_CONSTANTS.VALID_EMAIL,
        password: TEST_CONSTANTS.VALID_PASSWORD,
      };

      const hashedPassword = await AuthService.hashPassword(userData.password);

      // Mock user data
      mockQuery
        .mockResolvedValueOnce({
          rows: [{
            id: 'user-id',
            email: userData.email,
            password_hash: hashedPassword,
            email_confirmed: true,
            suspended: false,
            failed_login_attempts: 0,
            locked_until: null,
            first_name: 'John',
            last_name: 'Doe',
            role: 'nurse'
          }]
        })
        .mockResolvedValueOnce({ rows: [] }); // Update last login

      const result = await AuthService.login(userData.email, userData.password);

      expect(result.error).toBeUndefined();
      expect(result.auth).toBeDefined();
      expect(result.auth?.user.email).toBe(userData.email);
      expect(result.auth?.access_token).toBeDefined();
      expect(result.auth?.refresh_token).toBeDefined();
    });

    it('should reject login with invalid credentials', async () => {
      const userData = {
        email: TEST_CONSTANTS.VALID_EMAIL,
        password: 'wrongpassword',
      };

      // Mock no user found
      mockQuery.mockResolvedValueOnce({ rows: [] });

      const result = await AuthService.login(userData.email, userData.password);

      expect(result.error).toBe('Invalid email or password');
      expect(result.auth).toBeUndefined();
    });

    it('should reject login for suspended user', async () => {
      const userData = {
        email: TEST_CONSTANTS.VALID_EMAIL,
        password: TEST_CONSTANTS.VALID_PASSWORD,
      };

      // Mock suspended user
      mockQuery.mockResolvedValueOnce({
        rows: [{
          id: 'user-id',
          email: userData.email,
          suspended: true,
        }]
      });

      const result = await AuthService.login(userData.email, userData.password);

      expect(result.error).toBe('Account has been suspended. Please contact administrator.');
      expect(result.auth).toBeUndefined();
    });

    it('should reject login for locked user', async () => {
      const userData = {
        email: TEST_CONSTANTS.VALID_EMAIL,
        password: TEST_CONSTANTS.VALID_PASSWORD,
      };

      const futureDate = new Date();
      futureDate.setHours(futureDate.getHours() + 1);

      // Mock locked user
      mockQuery.mockResolvedValueOnce({
        rows: [{
          id: 'user-id',
          email: userData.email,
          suspended: false,
          locked_until: futureDate,
        }]
      });

      const result = await AuthService.login(userData.email, userData.password);

      expect(result.error).toContain('Account is locked due to too many failed login attempts');
      expect(result.auth).toBeUndefined();
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      const userId = 'user-id';
      const currentPassword = 'oldpassword';
      const newPassword = TEST_CONSTANTS.VALID_PASSWORD;
      const hashedCurrentPassword = await AuthService.hashPassword(currentPassword);

      // Mock current password verification
      mockQuery
        .mockResolvedValueOnce({
          rows: [{ password_hash: hashedCurrentPassword }]
        })
        .mockResolvedValueOnce({ rows: [] }); // Update password

      const result = await AuthService.changePassword(userId, currentPassword, newPassword);

      expect(result.error).toBeUndefined();
      expect(result.success).toBe(true);
    });

    it('should reject password change with wrong current password', async () => {
      const userId = 'user-id';
      const currentPassword = 'wrongpassword';
      const newPassword = TEST_CONSTANTS.VALID_PASSWORD;
      const hashedCurrentPassword = await AuthService.hashPassword('correctpassword');

      // Mock current password verification
      mockQuery.mockResolvedValueOnce({
        rows: [{ password_hash: hashedCurrentPassword }]
      });

      const result = await AuthService.changePassword(userId, currentPassword, newPassword);

      expect(result.error).toBe('Current password is incorrect');
      expect(result.success).toBeUndefined();
    });
  });

  describe('getUserById', () => {
    it('should get user by ID successfully', async () => {
      const userId = 'user-id';
      const userData = {
        id: userId,
        email: TEST_CONSTANTS.VALID_EMAIL,
        email_confirmed: true,
        first_name: 'John',
        last_name: 'Doe',
        role: 'nurse'
      };

      // Mock cache service
      const { cacheService } = require('../../src/services/cacheService');
      cacheService.getOrSet.mockResolvedValueOnce(userData);

      const result = await AuthService.getUserById(userId);

      expect(result).toEqual(userData);
    });

    it('should return null for non-existent user', async () => {
      const userId = 'non-existent-id';

      // Mock cache service returning null
      const { cacheService } = require('../../src/services/cacheService');
      cacheService.getOrSet.mockResolvedValueOnce(null);

      const result = await AuthService.getUserById(userId);

      expect(result).toBeNull();
    });
  });
});
