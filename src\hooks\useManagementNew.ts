// Management hook using API endpoints
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { managementApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

export function useManagement() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Departments
  const { data: departmentsData, isLoading: isLoadingDepartments } = useQuery({
    queryKey: ['departments'],
    queryFn: async () => {
      const response = await managementApi.getDepartments();
      return response.departments;
    },
  });

  const departments = departmentsData || [];

  // Roles
  const { data: rolesData, isLoading: isLoadingRoles } = useQuery({
    queryKey: ['roles'],
    queryFn: async () => {
      const response = await managementApi.getRoles();
      return response.roles;
    },
  });

  const roles = rolesData || [];

  // Positions
  const { data: positionsData, isLoading: isLoadingPositions } = useQuery({
    queryKey: ['positions'],
    queryFn: async () => {
      const response = await managementApi.getPositions();
      return response.positions;
    },
  });

  const positions = positionsData || [];

  // Department mutations
  const createDepartmentMutation = useMutation({
    mutationFn: async (departmentData: any) => {
      const response = await managementApi.createDepartment(departmentData);
      return response.department;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      toast({
        title: "Berhasil",
        description: "Departemen berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menambahkan departemen",
        variant: "destructive",
      });
    },
  });

  const updateDepartmentMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await managementApi.updateDepartment(id, data);
      return response.department;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      toast({
        title: "Berhasil",
        description: "Departemen berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui departemen",
        variant: "destructive",
      });
    },
  });

  const deleteDepartmentMutation = useMutation({
    mutationFn: async (id: string) => {
      await managementApi.deleteDepartment(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      toast({
        title: "Berhasil",
        description: "Departemen berhasil dihapus",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menghapus departemen",
        variant: "destructive",
      });
    },
  });

  // Role mutations
  const createRoleMutation = useMutation({
    mutationFn: async (roleData: any) => {
      const response = await managementApi.createRole(roleData);
      return response.role;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: "Berhasil",
        description: "Role berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menambahkan role",
        variant: "destructive",
      });
    },
  });

  const updateRoleMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await managementApi.updateRole(id, data);
      return response.role;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: "Berhasil",
        description: "Role berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui role",
        variant: "destructive",
      });
    },
  });

  // Position mutations
  const createPositionMutation = useMutation({
    mutationFn: async (positionData: any) => {
      const response = await managementApi.createPosition(positionData);
      return response.position;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['positions'] });
      toast({
        title: "Berhasil",
        description: "Posisi berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menambahkan posisi",
        variant: "destructive",
      });
    },
  });

  const updatePositionMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await managementApi.updatePosition(id, data);
      return response.position;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['positions'] });
      toast({
        title: "Berhasil",
        description: "Posisi berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui posisi",
        variant: "destructive",
      });
    },
  });

  return {
    // Data
    departments,
    roles,
    positions,
    
    // Loading states
    isLoadingDepartments,
    isLoadingRoles,
    isLoadingPositions,
    
    // Department actions
    createDepartment: createDepartmentMutation.mutate,
    updateDepartment: updateDepartmentMutation.mutate,
    deleteDepartment: deleteDepartmentMutation.mutate,
    
    // Role actions
    createRole: createRoleMutation.mutate,
    updateRole: updateRoleMutation.mutate,
    
    // Position actions
    createPosition: createPositionMutation.mutate,
    updatePosition: updatePositionMutation.mutate,
    
    // Mutation states
    isCreatingDepartment: createDepartmentMutation.isPending,
    isUpdatingDepartment: updateDepartmentMutation.isPending,
    isDeletingDepartment: deleteDepartmentMutation.isPending,
    isCreatingRole: createRoleMutation.isPending,
    isUpdatingRole: updateRoleMutation.isPending,
    isCreatingPosition: createPositionMutation.isPending,
    isUpdatingPosition: updatePositionMutation.isPending,
  };
}
