-- Update the handle_new_user function to handle role assignment
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, email, role)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data ->> 'first_name',
    NEW.raw_user_meta_data ->> 'last_name',
    NEW.email,
    COALESCE(
      (NEW.raw_user_meta_data ->> 'role')::employee_role,
      'nurse'::employee_role
    )
  );
  RETURN NEW;
END;
$$;