// Leave request service
import { query, transaction, LeaveRequest } from '@/config/database';

export interface LeaveRequestWithEmployee extends LeaveRequest {
  employee?: {
    first_name: string;
    last_name: string;
    department: string;
    position: string;
  };
}

export class LeaveRequestService {
  // Get all leave requests with employee information
  static async getAll(): Promise<LeaveRequestWithEmployee[]> {
    try {
      const result = await query(`
        SELECT lr.*, 
               e.first_name, e.last_name, e.department, e.position
        FROM leave_requests lr
        INNER JOIN employees e ON lr.employee_id = e.id
        ORDER BY lr.created_at DESC
      `);

      return result.rows.map(row => ({
        id: row.id,
        employee_id: row.employee_id,
        leave_type: row.leave_type,
        start_date: row.start_date,
        end_date: row.end_date,
        reason: row.reason,
        status: row.status,
        approved_by: row.approved_by,
        created_at: row.created_at,
        updated_at: row.updated_at,
        employee: {
          first_name: row.first_name,
          last_name: row.last_name,
          department: row.department,
          position: row.position,
        }
      }));
    } catch (error) {
      console.error('Get all leave requests error:', error);
      throw new Error('Failed to fetch leave requests');
    }
  }

  // Get leave request by ID
  static async getById(id: string): Promise<LeaveRequestWithEmployee | null> {
    try {
      const result = await query(`
        SELECT lr.*, 
               e.first_name, e.last_name, e.department, e.position
        FROM leave_requests lr
        INNER JOIN employees e ON lr.employee_id = e.id
        WHERE lr.id = $1
      `, [id]);

      if (result.rows.length === 0) return null;

      const row = result.rows[0];
      return {
        id: row.id,
        employee_id: row.employee_id,
        leave_type: row.leave_type,
        start_date: row.start_date,
        end_date: row.end_date,
        reason: row.reason,
        status: row.status,
        approved_by: row.approved_by,
        created_at: row.created_at,
        updated_at: row.updated_at,
        employee: {
          first_name: row.first_name,
          last_name: row.last_name,
          department: row.department,
          position: row.position,
        }
      };
    } catch (error) {
      console.error('Get leave request by ID error:', error);
      throw new Error('Failed to fetch leave request');
    }
  }

  // Get leave requests by employee ID
  static async getByEmployeeId(employeeId: string): Promise<LeaveRequestWithEmployee[]> {
    try {
      const result = await query(`
        SELECT lr.*, 
               e.first_name, e.last_name, e.department, e.position
        FROM leave_requests lr
        INNER JOIN employees e ON lr.employee_id = e.id
        WHERE lr.employee_id = $1
        ORDER BY lr.created_at DESC
      `, [employeeId]);

      return result.rows.map(row => ({
        id: row.id,
        employee_id: row.employee_id,
        leave_type: row.leave_type,
        start_date: row.start_date,
        end_date: row.end_date,
        reason: row.reason,
        status: row.status,
        approved_by: row.approved_by,
        created_at: row.created_at,
        updated_at: row.updated_at,
        employee: {
          first_name: row.first_name,
          last_name: row.last_name,
          department: row.department,
          position: row.position,
        }
      }));
    } catch (error) {
      console.error('Get leave requests by employee ID error:', error);
      throw new Error('Failed to fetch employee leave requests');
    }
  }

  // Get leave requests by status
  static async getByStatus(status: string): Promise<LeaveRequestWithEmployee[]> {
    try {
      const result = await query(`
        SELECT lr.*, 
               e.first_name, e.last_name, e.department, e.position
        FROM leave_requests lr
        INNER JOIN employees e ON lr.employee_id = e.id
        WHERE lr.status = $1
        ORDER BY lr.created_at DESC
      `, [status]);

      return result.rows.map(row => ({
        id: row.id,
        employee_id: row.employee_id,
        leave_type: row.leave_type,
        start_date: row.start_date,
        end_date: row.end_date,
        reason: row.reason,
        status: row.status,
        approved_by: row.approved_by,
        created_at: row.created_at,
        updated_at: row.updated_at,
        employee: {
          first_name: row.first_name,
          last_name: row.last_name,
          department: row.department,
          position: row.position,
        }
      }));
    } catch (error) {
      console.error('Get leave requests by status error:', error);
      throw new Error('Failed to fetch leave requests by status');
    }
  }

  // Create leave request
  static async create(leaveData: Omit<LeaveRequest, 'id' | 'created_at' | 'updated_at'>): Promise<LeaveRequest> {
    try {
      // Check if employee exists
      const employeeCheck = await query(
        'SELECT id FROM employees WHERE id = $1',
        [leaveData.employee_id]
      );
      if (employeeCheck.rows.length === 0) {
        throw new Error('Employee not found');
      }

      // Check for overlapping leave requests
      const overlapCheck = await query(`
        SELECT id FROM leave_requests 
        WHERE employee_id = $1 
        AND status != 'rejected'
        AND (
          (start_date <= $2 AND end_date >= $2) OR
          (start_date <= $3 AND end_date >= $3) OR
          (start_date >= $2 AND end_date <= $3)
        )
      `, [leaveData.employee_id, leaveData.start_date, leaveData.end_date]);

      if (overlapCheck.rows.length > 0) {
        throw new Error('Overlapping leave request exists for this period');
      }

      const result = await query(`
        INSERT INTO leave_requests (
          employee_id, leave_type, start_date, end_date, 
          reason, status, approved_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *
      `, [
        leaveData.employee_id,
        leaveData.leave_type,
        leaveData.start_date,
        leaveData.end_date,
        leaveData.reason,
        leaveData.status || 'pending',
        leaveData.approved_by
      ]);

      return result.rows[0];
    } catch (error) {
      console.error('Create leave request error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create leave request');
    }
  }

  // Update leave request
  static async update(id: string, leaveData: Partial<LeaveRequest>, approvedBy?: string): Promise<LeaveRequest> {
    try {
      // Remove fields that shouldn't be updated
      const { id: _, created_at, updated_at, ...updateData } = leaveData;
      
      const fields = Object.keys(updateData);
      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      // Check if leave request exists
      const existingRequest = await this.getById(id);
      if (!existingRequest) {
        throw new Error('Leave request not found');
      }

      // If updating dates, check for overlaps
      if (updateData.start_date || updateData.end_date) {
        const checkStartDate = updateData.start_date || existingRequest.start_date;
        const checkEndDate = updateData.end_date || existingRequest.end_date;

        const overlapCheck = await query(`
          SELECT id FROM leave_requests 
          WHERE employee_id = $1 
          AND id != $2
          AND status != 'rejected'
          AND (
            (start_date <= $3 AND end_date >= $3) OR
            (start_date <= $4 AND end_date >= $4) OR
            (start_date >= $3 AND end_date <= $4)
          )
        `, [existingRequest.employee_id, id, checkStartDate, checkEndDate]);

        if (overlapCheck.rows.length > 0) {
          throw new Error('Overlapping leave request exists for this period');
        }
      }

      // If status is being updated to approved/rejected, set approved_by
      if (updateData.status && (updateData.status === 'approved' || updateData.status === 'rejected') && approvedBy) {
        updateData.approved_by = approvedBy;
        fields.push('approved_by');
      }

      const values = fields.map(field => updateData[field as keyof LeaveRequest]);
      const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

      const result = await query(`
        UPDATE leave_requests 
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `, [id, ...values]);

      if (result.rows.length === 0) {
        throw new Error('Leave request not found');
      }

      return result.rows[0];
    } catch (error) {
      console.error('Update leave request error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update leave request');
    }
  }

  // Delete leave request
  static async delete(id: string): Promise<void> {
    try {
      const result = await query('DELETE FROM leave_requests WHERE id = $1', [id]);
      
      if (result.rowCount === 0) {
        throw new Error('Leave request not found');
      }
    } catch (error) {
      console.error('Delete leave request error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete leave request');
    }
  }

  // Get leave request statistics
  static async getStatistics(): Promise<any> {
    try {
      const totalResult = await query('SELECT COUNT(*) as total FROM leave_requests');
      const statusResult = await query(`
        SELECT status, COUNT(*) as count 
        FROM leave_requests 
        GROUP BY status 
        ORDER BY count DESC
      `);
      const typeResult = await query(`
        SELECT leave_type, COUNT(*) as count 
        FROM leave_requests 
        GROUP BY leave_type 
        ORDER BY count DESC
      `);

      return {
        total: parseInt(totalResult.rows[0].total),
        byStatus: statusResult.rows,
        byType: typeResult.rows
      };
    } catch (error) {
      console.error('Get leave request statistics error:', error);
      throw new Error('Failed to get leave request statistics');
    }
  }
}
