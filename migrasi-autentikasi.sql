ALTER TABLE public.users
ADD COLUMN suspended boolean DEFAULT false,
ADD COLUMN    failed_login_attempts integer DEFAULT 0,
ADD COLUMN    locked_until timestamp with time zone,
ADD COLUMN    last_login timestamp with time zone;

-- Create password_resets table
CREATE TABLE public.password_resets (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    user_id uuid NOT NULL,
    token_hash text NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE,
    UNIQUE(user_id)
);

-- Create email_verifications table
CREATE TABLE public.email_verifications (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    user_id uuid NOT NULL,
    token_hash text NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE,
    UNIQUE(user_id)
);

-- Create user_activity_logs table
CREATE TABLE public.user_activity_logs (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    user_id uuid,
    action text NOT NULL,
    resource text,
    resource_id uuid,
    ip_address inet,
    user_agent text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL
);

CREATE INDEX idx_password_resets_user_id ON public.password_resets(user_id);
CREATE INDEX idx_password_resets_expires_at ON public.password_resets(expires_at);

-- Create cleanup function for expired password reset tokens
CREATE OR REPLACE FUNCTION cleanup_expired_password_resets()
RETURNS void AS $$
BEGIN
    DELETE FROM public.password_resets WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;