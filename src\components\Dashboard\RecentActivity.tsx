
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useEmployees } from '@/hooks/useEmployeesNew';
import { Calendar, UserPlus, Edit, Trash2 } from 'lucide-react';

export function RecentActivity() {
  const { employees, isLoading } = useEmployees();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Aktivitas Terbaru</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Sort employees by creation date (most recent first)
  const recentEmployees = [...employees]
    .sort((a, b) => new Date(b.joinDate).getTime() - new Date(a.joinDate).getTime())
    .slice(0, 5);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="h-5 w-5" />
          <span>Aktivitas Terbaru</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentEmployees.map((employee) => (
            <div key={employee.id} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                <UserPlus className="h-4 w-4 text-green-600" />
                <div>
                  <p className="font-medium">
                    {employee.firstName} {employee.lastName}
                  </p>
                  <p className="text-sm text-gray-600">
                    Bergabung sebagai {employee.position}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <Badge variant="outline" className="mb-1">
                  {employee.department}
                </Badge>
                <p className="text-xs text-gray-500">
                  {new Date(employee.joinDate).toLocaleDateString('id-ID')}
                </p>
              </div>
            </div>
          ))}
          
          {recentEmployees.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>Belum ada aktivitas terbaru</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
