
export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions?: any;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Department {
  id: string;
  name: string;
  description?: string;
  managerId?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  manager?: {
    firstName: string;
    lastName: string;
  };
}

export interface Position {
  id: string;
  name: string;
  description?: string;
  departmentId?: string;
  roleId?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  department?: {
    name: string;
  };
  role?: {
    name: string;
  };
}
