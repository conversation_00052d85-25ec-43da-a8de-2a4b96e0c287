// Simple in-memory cache service
// In production, consider using Redis for distributed caching

interface CacheItem<T> {
  data: T;
  expiresAt: number;
  createdAt: number;
}

class CacheService {
  private cache = new Map<string, CacheItem<any>>();
  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL

  // Set cache item with TTL
  set<T>(key: string, data: T, ttlMs?: number): void {
    const ttl = ttlMs || this.defaultTTL;
    const expiresAt = Date.now() + ttl;
    
    this.cache.set(key, {
      data,
      expiresAt,
      createdAt: Date.now()
    });
  }

  // Get cache item
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }
    
    // Check if expired
    if (Date.now() > item.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data as T;
  }

  // Delete cache item
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  // Clear all cache
  clear(): void {
    this.cache.clear();
  }

  // Get cache statistics
  getStats() {
    const now = Date.now();
    let expired = 0;
    let active = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiresAt) {
        expired++;
      } else {
        active++;
      }
    }
    
    return {
      total: this.cache.size,
      active,
      expired,
      memoryUsage: process.memoryUsage()
    };
  }

  // Clean up expired items
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiresAt) {
        this.cache.delete(key);
        cleaned++;
      }
    }
    
    return cleaned;
  }

  // Cache with function execution
  async getOrSet<T>(
    key: string, 
    fetchFunction: () => Promise<T>, 
    ttlMs?: number
  ): Promise<T> {
    // Try to get from cache first
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }
    
    // Execute function and cache result
    try {
      const result = await fetchFunction();
      this.set(key, result, ttlMs);
      return result;
    } catch (error) {
      // Don't cache errors
      throw error;
    }
  }

  // Invalidate cache by pattern
  invalidatePattern(pattern: string): number {
    let invalidated = 0;
    const regex = new RegExp(pattern);
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        invalidated++;
      }
    }
    
    return invalidated;
  }
}

// Create singleton instance
export const cacheService = new CacheService();

// Cache key generators
export const CacheKeys = {
  user: (id: string) => `user:${id}`,
  userProfile: (id: string) => `user:profile:${id}`,
  userByEmail: (email: string) => `user:email:${email}`,
  employee: (id: string) => `employee:${id}`,
  employeeByUserId: (userId: string) => `employee:user:${userId}`,
  schedule: (id: string) => `schedule:${id}`,
  schedulesByEmployee: (employeeId: string, date: string) => `schedules:employee:${employeeId}:${date}`,
  leaveRequest: (id: string) => `leave:${id}`,
  leaveRequestsByEmployee: (employeeId: string) => `leave:employee:${employeeId}`,
  stats: (type: string) => `stats:${type}`,
  activityLogs: (userId: string, page: number) => `activity:${userId}:${page}`,
} as const;

// Cache TTL constants (in milliseconds)
export const CacheTTL = {
  SHORT: 1 * 60 * 1000,      // 1 minute
  MEDIUM: 5 * 60 * 1000,     // 5 minutes
  LONG: 30 * 60 * 1000,      // 30 minutes
  VERY_LONG: 2 * 60 * 60 * 1000, // 2 hours
} as const;

// Cleanup expired cache items every 5 minutes
setInterval(() => {
  const cleaned = cacheService.cleanup();
  if (cleaned > 0) {
    console.log(`🧹 Cache cleanup: removed ${cleaned} expired items`);
  }
}, 5 * 60 * 1000);

// Cache middleware for Express routes
export const cacheMiddleware = (keyGenerator: (req: any) => string, ttl: number = CacheTTL.MEDIUM) => {
  return async (req: any, res: any, next: any) => {
    const key = keyGenerator(req);
    const cached = cacheService.get(key);
    
    if (cached) {
      return res.json(cached);
    }
    
    // Store original json method
    const originalJson = res.json;
    
    // Override json method to cache response
    res.json = function(data: any) {
      if (res.statusCode === 200) {
        cacheService.set(key, data, ttl);
      }
      return originalJson.call(this, data);
    };
    
    next();
  };
};

// Helper function to invalidate user-related cache
export const invalidateUserCache = (userId: string): void => {
  cacheService.delete(CacheKeys.user(userId));
  cacheService.delete(CacheKeys.userProfile(userId));
  cacheService.invalidatePattern(`user:.*:${userId}`);
  cacheService.invalidatePattern(`employee:user:${userId}`);
};

// Helper function to invalidate employee-related cache
export const invalidateEmployeeCache = (employeeId: string): void => {
  cacheService.delete(CacheKeys.employee(employeeId));
  cacheService.invalidatePattern(`schedules:employee:${employeeId}:.*`);
  cacheService.invalidatePattern(`leave:employee:${employeeId}`);
};

export default cacheService;
