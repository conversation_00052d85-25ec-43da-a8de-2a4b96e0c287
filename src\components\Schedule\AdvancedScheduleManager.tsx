
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RotatingShiftGenerator } from './RotatingShiftGenerator';
import { ShiftRotationAnalyzer } from './ShiftRotationAnalyzer';
import { useSchedules } from '@/hooks/useSchedulesNew';
import { RotateCcw, BarChart3, Settings, Clock } from 'lucide-react';

export function AdvancedScheduleManager() {
  const { schedules } = useSchedules();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Advanced Schedule Manager</span>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Ke<PERSON>la sistem penja<PERSON>walan rotating shift dengan analisis mendalam
          </p>
        </CardHeader>
      </Card>

      <Tabs defaultValue="generator" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="generator" className="flex items-center space-x-2">
            <RotateCcw className="h-4 w-4" />
            <span>Rotating Shift Generator</span>
          </TabsTrigger>
          <TabsTrigger value="analyzer" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analisis & Laporan</span>
          </TabsTrigger>
          <TabsTrigger value="optimizer" className="flex items-center space-x-2">
            <Clock className="h-4 w-4" />
            <span>Optimasi Jadwal</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generator">
          <RotatingShiftGenerator />
        </TabsContent>

        <TabsContent value="analyzer">
          <ShiftRotationAnalyzer schedules={schedules} />
        </TabsContent>

        <TabsContent value="optimizer">
          <Card>
            <CardHeader>
              <CardTitle>Optimasi Jadwal</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">Fitur Optimasi Jadwal</p>
                <p className="text-sm mt-2">
                  Akan segera hadir - optimasi otomatis untuk distribusi shift yang lebih baik
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
