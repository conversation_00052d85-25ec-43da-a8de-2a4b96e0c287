
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { EmployeeCard } from '../components/EmployeeCard';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Employee, EmployeeFilters } from '../types/employee';
import { Search, Plus, Filter, Users, Download, Upload } from 'lucide-react';
import { useEmployees } from '@/hooks/useEmployeesNew';

export default function Employees() {
  const navigate = useNavigate();
  const { employees, isLoading, error } = useEmployees();
  const [filters, setFilters] = useState<EmployeeFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = !filters.search || 
      `${employee.firstName} ${employee.lastName}`.toLowerCase().includes(filters.search.toLowerCase()) ||
      employee.employeeId.toLowerCase().includes(filters.search.toLowerCase()) ||
      employee.email.toLowerCase().includes(filters.search.toLowerCase());

    const matchesRole = !filters.role || employee.role === filters.role;
    const matchesDepartment = !filters.department || employee.department === filters.department;
    const matchesStatus = !filters.status || employee.status === filters.status;
    const matchesShift = !filters.shift || employee.shift === filters.shift;

    return matchesSearch && matchesRole && matchesDepartment && matchesStatus && matchesShift;
  });

  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  const handleFilterChange = (key: keyof EmployeeFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value || undefined }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  const handleViewEmployee = (employee: Employee) => {
    navigate(`/employees/${employee.id}`);
  };

  const handleEditEmployee = (employee: Employee) => {
    navigate(`/employees/${employee.id}/edit`);
  };

  const handleExportData = () => {
    // Create CSV data
    const csvData = [
      ['ID', 'Nama', 'Email', 'Role', 'Departemen', 'Status', 'Shift', 'Tanggal Bergabung'],
      ...filteredEmployees.map(emp => [
        emp.employeeId,
        `${emp.firstName} ${emp.lastName}`,
        emp.email,
        emp.role,
        emp.department,
        emp.status,
        emp.shift,
        new Date(emp.joinDate).toLocaleDateString('id-ID')
      ])
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `employees_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="p-6 text-center">
          <p className="text-red-600">Error loading employees. Please try again.</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Manajemen Karyawan</h1>
          <p className="text-gray-600">Kelola data karyawan rumah sakit</p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>Filter</span>
          </Button>
          <Button
            variant="outline"
            onClick={handleExportData}
            className="flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </Button>
          <Button 
            onClick={() => navigate('/employees/add')}
            className="flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Tambah Karyawan</span>
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="p-6">
        <div className="flex items-center space-x-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Cari karyawan..."
              value={filters.search || ''}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="text-sm text-gray-600">
            Menampilkan {filteredEmployees.length} dari {employees.length} karyawan
          </div>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t">
            <Select value={filters.role || ''} onValueChange={(value) => handleFilterChange('role', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih Role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Role</SelectItem>
                <SelectItem value="doctor">Dokter</SelectItem>
                <SelectItem value="nurse">Perawat</SelectItem>
                <SelectItem value="pharmacist">Apoteker</SelectItem>
                <SelectItem value="technician">Teknisi</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="receptionist">Resepsionis</SelectItem>
                <SelectItem value="manager">Manager</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.department || ''} onValueChange={(value) => handleFilterChange('department', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih Departemen" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Departemen</SelectItem>
                <SelectItem value="emergency">Emergency</SelectItem>
                <SelectItem value="surgery">Surgery</SelectItem>
                <SelectItem value="pediatrics">Pediatrics</SelectItem>
                <SelectItem value="cardiology">Cardiology</SelectItem>
                <SelectItem value="pharmacy">Pharmacy</SelectItem>
                <SelectItem value="laboratory">Laboratory</SelectItem>
                <SelectItem value="radiology">Radiology</SelectItem>
                <SelectItem value="administration">Administration</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.status || ''} onValueChange={(value) => handleFilterChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Status</SelectItem>
                <SelectItem value="active">Aktif</SelectItem>
                <SelectItem value="inactive">Tidak Aktif</SelectItem>
                <SelectItem value="on_leave">Cuti</SelectItem>
                <SelectItem value="terminated">Diberhentikan</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={clearFilters}>
              Reset Filter
            </Button>
          </div>
        )}
      </Card>

      {/* Employee Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredEmployees.map((employee) => (
          <EmployeeCard
            key={employee.id}
            employee={employee}
            onView={handleViewEmployee}
            onEdit={handleEditEmployee}
          />
        ))}
      </div>

      {filteredEmployees.length === 0 && (
        <Card className="p-12 text-center">
          <div className="text-gray-500">
            <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium mb-2">Tidak ada karyawan ditemukan</h3>
            <p className="text-sm">Coba ubah filter pencarian atau tambah karyawan baru</p>
          </div>
        </Card>
      )}
    </div>
  );
}
