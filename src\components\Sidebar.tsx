
import { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  Users, 
  Calendar, 
  Settings, 
  Bell, 
  Database,
  Activity,
  Heart,
  Stethoscope,
  Shield
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui/sidebar';
import { RoleBasedMenuItem } from './RoleBasedMenuItem';
import { EmployeeRole } from '@/types/employee';

const navigationItems = [
  { 
    title: 'Dashboard', 
    url: '/', 
    icon: Activity,
    allowedRoles: ['admin', 'doctor', 'nurse', 'pharmacist', 'technician', 'receptionist', 'manager'] as EmployeeRole[]
  },
  { 
    title: '<PERSON><PERSON>wan', 
    url: '/employees', 
    icon: Users,
    allowedRoles: ['admin', 'manager'] as EmployeeRole[]
  },
  { 
    title: 'Jadwal', 
    url: '/schedule', 
    icon: Calendar,
    allowedRoles: ['admin', 'manager', 'doctor', 'nurse'] as Employee<PERSON>ole[]
  },
  { 
    title: 'Manajemen', 
    url: '/management', 
    icon: Shield,
    allowedRoles: ['admin'] as EmployeeRole[]
  },
  { 
    title: 'Laporan', 
    url: '/reports', 
    icon: Database,
    allowedRoles: ['admin', 'manager', 'doctor'] as EmployeeRole[]
  },
  { 
    title: 'Notifikasi', 
    url: '/notifications', 
    icon: Bell,
    allowedRoles: ['admin', 'doctor', 'nurse', 'pharmacist', 'technician', 'receptionist', 'manager'] as EmployeeRole[]
  },
  { 
    title: 'Pengaturan', 
    url: '/settings', 
    icon: Settings,
    allowedRoles: ['admin', 'doctor', 'nurse', 'pharmacist', 'technician', 'receptionist', 'manager'] as EmployeeRole[]
  },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const location = useLocation();
  const currentPath = location.pathname;
  const collapsed = state === 'collapsed';

  const isActive = (path: string) => {
    if (path === '/') return currentPath === '/';
    return currentPath.startsWith(path);
  };

  const getNavCls = (path: string) =>
    isActive(path) 
      ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium" 
      : "hover:bg-sidebar-accent/50 text-sidebar-foreground";

  return (
    <Sidebar className={collapsed ? "w-16" : "w-64"} collapsible="icon">
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="flex items-center gap-2 px-4 py-3">
          <div className="flex items-center justify-center w-8 h-8 bg-white rounded-lg">
            <Heart className="w-5 h-5 text-primary" />
          </div>
          {!collapsed && (
            <div>
              <h2 className="text-lg font-bold text-sidebar-primary">MediCare</h2>
              <p className="text-xs text-sidebar-foreground/70">Sistem Manajemen</p>
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="text-sidebar-foreground/70">
            {!collapsed && "Menu Utama"}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => (
                <RoleBasedMenuItem key={item.title} allowedRoles={item.allowedRoles}>
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <NavLink 
                        to={item.url} 
                        className={getNavCls(item.url)}
                        title={collapsed ? item.title : undefined}
                      >
                        <item.icon className="w-4 h-4" />
                        {!collapsed && <span>{item.title}</span>}
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </RoleBasedMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <div className="p-2 border-t border-sidebar-border">
        <SidebarTrigger className="w-full" />
      </div>
    </Sidebar>
  );
}
