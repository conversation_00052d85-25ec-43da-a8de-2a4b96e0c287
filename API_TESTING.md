# API Testing Guide

This document provides comprehensive testing instructions for the Express.js API endpoints.

## 🧪 Testing Setup

### Prerequisites
1. Backend server running on `http://localhost:3001`
2. PostgreSQL database set up with test data
3. curl or Postman for API testing

### Environment Variables
Ensure your backend `.env` file is configured:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=manajemen_karyawan
DB_USER=app_user
DB_PASSWORD=your_password
JWT_SECRET=your-super-secret-jwt-key
NODE_ENV=development
PORT=3001
```

## 🔐 Authentication Tests

### 1. Health Check
```bash
curl -X GET http://localhost:3001/health
```
Expected: `{"status":"OK","timestamp":"...","uptime":...}`

### 2. API Info
```bash
curl -X GET http://localhost:3001/api/v1
```
Expected: API information with available endpoints

### 3. User Registration
```bash
curl -X POST http://localhost:3001/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User",
    "role": "nurse"
  }'
```
Expected: `{"message":"User registered successfully","user":{...}}`

### 4. User Login
```bash
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```
Expected: `{"message":"Login successful","user":{...},"access_token":"...","expires_in":604800}`

**Save the access_token for subsequent requests!**

### 5. Get Current User
```bash
curl -X GET http://localhost:3001/api/v1/auth/me \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```
Expected: `{"user":{...}}`

### 6. Update Profile
```bash
curl -X PUT http://localhost:3001/api/v1/auth/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Updated",
    "lastName": "Name"
  }'
```
Expected: `{"message":"Profile updated successfully","profile":{...}}`

### 7. Logout
```bash
curl -X POST http://localhost:3001/api/v1/auth/logout \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```
Expected: `{"message":"Logout successful"}`

## 👥 Employee Management Tests

### 1. Get All Employees (Admin/Manager only)
```bash
curl -X GET http://localhost:3001/api/v1/employees \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. Create Employee (Admin/Manager only)
```bash
curl -X POST http://localhost:3001/api/v1/employees \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "employeeId": "EMP001",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "role": "doctor",
    "department": "emergency",
    "position": "Emergency Physician",
    "joinDate": "2024-01-15",
    "status": "active",
    "shift": "morning",
    "salary": 75000
  }'
```

### 3. Get Employee by ID
```bash
curl -X GET http://localhost:3001/api/v1/employees/EMPLOYEE_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. Update Employee
```bash
curl -X PUT http://localhost:3001/api/v1/employees/EMPLOYEE_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+1234567891",
    "salary": 80000
  }'
```

### 5. Delete Employee (Admin/Manager only)
```bash
curl -X DELETE http://localhost:3001/api/v1/employees/EMPLOYEE_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 6. Get Employee Statistics (Admin/Manager only)
```bash
curl -X GET http://localhost:3001/api/v1/employees/statistics \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 📅 Schedule Management Tests

### 1. Get All Schedules
```bash
curl -X GET http://localhost:3001/api/v1/schedules \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. Create Schedule (Admin/Manager only)
```bash
curl -X POST http://localhost:3001/api/v1/schedules \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "employeeId": "EMPLOYEE_ID",
    "shiftDate": "2024-01-20",
    "shiftType": "morning",
    "startTime": "08:00",
    "endTime": "16:00",
    "status": "scheduled",
    "notes": "Regular morning shift"
  }'
```

### 3. Update Schedule
```bash
curl -X PUT http://localhost:3001/api/v1/schedules/SCHEDULE_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "completed",
    "notes": "Shift completed successfully"
  }'
```

### 4. Delete Schedule (Admin/Manager only)
```bash
curl -X DELETE http://localhost:3001/api/v1/schedules/SCHEDULE_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🏖️ Leave Request Tests

### 1. Get All Leave Requests
```bash
curl -X GET http://localhost:3001/api/v1/leave-requests \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. Create Leave Request
```bash
curl -X POST http://localhost:3001/api/v1/leave-requests \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "employeeId": "EMPLOYEE_ID",
    "leaveType": "Annual Leave",
    "startDate": "2024-02-01",
    "endDate": "2024-02-05",
    "reason": "Family vacation",
    "status": "pending"
  }'
```

### 3. Approve Leave Request (Admin/Manager only)
```bash
curl -X PATCH http://localhost:3001/api/v1/leave-requests/LEAVE_REQUEST_ID/approve \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. Reject Leave Request (Admin/Manager only)
```bash
curl -X PATCH http://localhost:3001/api/v1/leave-requests/LEAVE_REQUEST_ID/reject \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🏢 Management Tests

### 1. Get All Departments
```bash
curl -X GET http://localhost:3001/api/v1/management/departments \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. Create Department (Admin only)
```bash
curl -X POST http://localhost:3001/api/v1/management/departments \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Neurology",
    "description": "Neurological disorders treatment",
    "managerId": "MANAGER_EMPLOYEE_ID"
  }'
```

### 3. Get All Roles
```bash
curl -X GET http://localhost:3001/api/v1/management/roles \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. Create Role (Admin only)
```bash
curl -X POST http://localhost:3001/api/v1/management/roles \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Senior Doctor",
    "description": "Senior medical practitioner",
    "permissions": {
      "canPrescribe": true,
      "canSupervise": true,
      "canApproveLeave": true
    }
  }'
```

## 🧪 Error Testing

### 1. Test Invalid Authentication
```bash
curl -X GET http://localhost:3001/api/v1/employees \
  -H "Authorization: Bearer invalid_token"
```
Expected: `{"error":"Invalid or expired token"}`

### 2. Test Missing Required Fields
```bash
curl -X POST http://localhost:3001/api/v1/employees \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John"
  }'
```
Expected: Validation error with missing fields

### 3. Test Unauthorized Access
```bash
# Login as non-admin user, then try to create department
curl -X POST http://localhost:3001/api/v1/management/departments \
  -H "Authorization: Bearer NON_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Department"
  }'
```
Expected: `{"error":"Insufficient permissions"}`

## 📊 Performance Testing

### 1. Rate Limiting Test
```bash
# Send multiple requests quickly to test rate limiting
for i in {1..10}; do
  curl -X GET http://localhost:3001/api/v1/employees \
    -H "Authorization: Bearer YOUR_ACCESS_TOKEN" &
done
wait
```

### 2. Large Data Test
```bash
# Create multiple employees to test pagination
for i in {1..50}; do
  curl -X POST http://localhost:3001/api/v1/employees \
    -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
      \"employeeId\": \"EMP$i\",
      \"firstName\": \"Employee\",
      \"lastName\": \"$i\",
      \"email\": \"employee$<EMAIL>\",
      \"role\": \"nurse\",
      \"department\": \"emergency\",
      \"position\": \"Staff Nurse\",
      \"joinDate\": \"2024-01-01\",
      \"shift\": \"morning\"
    }"
done
```

## 🔍 Debugging Tips

### 1. Check Server Logs
```bash
# In backend directory
npm run dev
# Watch for detailed error logs
```

### 2. Database Connection Test
```bash
# Test direct database connection
psql -U app_user -d manajemen_karyawan -c "SELECT COUNT(*) FROM users;"
```

### 3. JWT Token Debugging
```bash
# Decode JWT token (use online JWT decoder)
echo "YOUR_ACCESS_TOKEN" | base64 -d
```

### 4. Network Issues
```bash
# Test if server is responding
curl -I http://localhost:3001/health
```

## ✅ Test Checklist

- [ ] Health check endpoint responds
- [ ] User registration works
- [ ] User login returns valid JWT
- [ ] Protected endpoints require authentication
- [ ] Role-based access control works
- [ ] CRUD operations for all entities work
- [ ] Input validation catches invalid data
- [ ] Error responses are properly formatted
- [ ] Rate limiting prevents abuse
- [ ] Database transactions work correctly
- [ ] File upload/download works (if implemented)
- [ ] API documentation is accessible

## 📝 Test Results Template

```
Test Date: ___________
Tester: ___________
Environment: Development/Staging/Production

Authentication Tests:
- [ ] Registration: Pass/Fail
- [ ] Login: Pass/Fail
- [ ] Token refresh: Pass/Fail
- [ ] Logout: Pass/Fail

Employee Management:
- [ ] Create: Pass/Fail
- [ ] Read: Pass/Fail
- [ ] Update: Pass/Fail
- [ ] Delete: Pass/Fail

Schedule Management:
- [ ] Create: Pass/Fail
- [ ] Read: Pass/Fail
- [ ] Update: Pass/Fail
- [ ] Delete: Pass/Fail

Leave Requests:
- [ ] Create: Pass/Fail
- [ ] Approve/Reject: Pass/Fail
- [ ] Read: Pass/Fail

Management:
- [ ] Departments: Pass/Fail
- [ ] Roles: Pass/Fail
- [ ] Positions: Pass/Fail

Error Handling:
- [ ] Invalid auth: Pass/Fail
- [ ] Missing fields: Pass/Fail
- [ ] Unauthorized access: Pass/Fail

Performance:
- [ ] Rate limiting: Pass/Fail
- [ ] Large data sets: Pass/Fail

Notes:
___________
```
