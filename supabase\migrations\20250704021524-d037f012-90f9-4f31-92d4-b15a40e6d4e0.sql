
-- Add user_id column to employees table to link with auth.users
ALTER TABLE public.employees 
ADD COLUMN user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;

-- Create index for better performance
CREATE INDEX idx_employees_user_id ON public.employees(user_id);

-- <PERSON>reate function to sync employee data with profiles when account is created
CREATE OR REPLACE FUNCTION public.sync_employee_profile()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  -- Update employee record with user_id when profile is created
  UPDATE public.employees 
  SET user_id = NEW.id
  WHERE email = NEW.email AND user_id IS NULL;
  
  RETURN NEW;
END;
$$;

-- <PERSON>reate trigger to sync employee data when profile is created
CREATE TRIGGER on_profile_created
  AFTER INSERT ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.sync_employee_profile();

-- Update existing employees who already have accounts
UPDATE public.employees 
SET user_id = profiles.id
FROM public.profiles
WHERE employees.email = profiles.email 
AND employees.user_id IS NULL;
