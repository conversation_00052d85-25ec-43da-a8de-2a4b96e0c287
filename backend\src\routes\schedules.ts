// Schedule routes
import { Router, Response } from 'express';
import { ScheduleService } from '@/services/scheduleService';
import { authenticateToken, requireManagerOrAdmin, AuthRequest } from '@/middleware/auth';
import { validate, scheduleSchemas } from '@/middleware/validation';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all schedules
router.get('/', async (req: AuthRequest, res: Response) => {
  try {
    const { employeeId, startDate, endDate } = req.query;

    let schedules;

    if (employeeId) {
      // Users can view their own schedules, managers/admins can view any
      if (req.user!.role !== 'admin' && req.user!.role !== 'manager') {
        // Check if user is requesting their own schedules
        // This would require additional logic to map user ID to employee ID
        // For now, we'll allow all authenticated users to view schedules
      }
      schedules = await ScheduleService.getByEmployeeId(employeeId as string);
    } else if (startDate && endDate) {
      schedules = await ScheduleService.getByDateRange(
        new Date(startDate as string),
        new Date(endDate as string)
      );
    } else {
      schedules = await ScheduleService.getAll();
    }

    res.json({
      schedules,
      total: schedules.length
    });
  } catch (error) {
    console.error('Get schedules endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch schedules' });
  }
});

// Get schedule statistics (Manager/Admin only)
router.get('/statistics', requireManagerOrAdmin, async (req: AuthRequest, res: Response) => {
  try {
    const { startDate, endDate } = req.query;

    const statistics = await ScheduleService.getStatistics(
      startDate ? new Date(startDate as string) : undefined,
      endDate ? new Date(endDate as string) : undefined
    );

    res.json(statistics);
  } catch (error) {
    console.error('Get schedule statistics endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch schedule statistics' });
  }
});

// Get schedule by ID
router.get('/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const schedule = await ScheduleService.getById(id);
    
    if (!schedule) {
      return res.status(404).json({ error: 'Schedule not found' });
    }

    // Check if user can access this schedule
    // Users can view their own schedules, managers/admins can view any
    // This would require additional logic to check if the schedule belongs to the user
    // For now, we'll allow all authenticated users to view schedules

    res.json({ schedule });
  } catch (error) {
    console.error('Get schedule by ID endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch schedule' });
  }
});

// Create schedule (Manager/Admin only)
router.post('/', requireManagerOrAdmin, validate(scheduleSchemas.create), async (req: AuthRequest, res: Response) => {
  try {
    const scheduleData = {
      employee_id: req.body.employeeId,
      shift_date: new Date(req.body.shiftDate),
      shift_type: req.body.shiftType,
      start_time: req.body.startTime,
      end_time: req.body.endTime,
      status: req.body.status || 'scheduled',
      notes: req.body.notes,
    };

    const schedule = await ScheduleService.create(scheduleData, req.user!.id);

    res.status(201).json({
      message: 'Schedule created successfully',
      schedule
    });
  } catch (error) {
    console.error('Create schedule endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({ error: error.message });
      }
      if (error.message.includes('conflict')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to create schedule' });
  }
});

// Update schedule
router.put('/:id', validate(scheduleSchemas.update), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    // Check if user can update this schedule
    const existingSchedule = await ScheduleService.getById(id);
    if (!existingSchedule) {
      return res.status(404).json({ error: 'Schedule not found' });
    }

    // Only managers/admins can update schedules, or the employee can update their own schedule status
    const canUpdate = 
      req.user!.role === 'admin' || 
      req.user!.role === 'manager';
      // TODO: Add logic to check if user owns this schedule

    if (!canUpdate) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Convert camelCase to snake_case for database
    const updateData: any = {};
    if (req.body.employeeId !== undefined) updateData.employee_id = req.body.employeeId;
    if (req.body.shiftDate !== undefined) updateData.shift_date = new Date(req.body.shiftDate);
    if (req.body.shiftType !== undefined) updateData.shift_type = req.body.shiftType;
    if (req.body.startTime !== undefined) updateData.start_time = req.body.startTime;
    if (req.body.endTime !== undefined) updateData.end_time = req.body.endTime;
    if (req.body.status !== undefined) updateData.status = req.body.status;
    if (req.body.notes !== undefined) updateData.notes = req.body.notes;

    const schedule = await ScheduleService.update(id, updateData);

    res.json({
      message: 'Schedule updated successfully',
      schedule
    });
  } catch (error) {
    console.error('Update schedule endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Schedule not found') {
        return res.status(404).json({ error: error.message });
      }
      if (error.message.includes('conflict')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to update schedule' });
  }
});

// Delete schedule (Manager/Admin only)
router.delete('/:id', requireManagerOrAdmin, async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    await ScheduleService.delete(id);

    res.json({ message: 'Schedule deleted successfully' });
  } catch (error) {
    console.error('Delete schedule endpoint error:', error);
    
    if (error instanceof Error && error.message === 'Schedule not found') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to delete schedule' });
  }
});

export default router;
