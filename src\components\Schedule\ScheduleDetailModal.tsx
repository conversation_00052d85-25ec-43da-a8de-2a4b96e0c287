
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Clock, User, Calendar, Edit, Trash2 } from 'lucide-react';
import { Schedule } from '@/types/schedule';
import { useSchedules } from '@/hooks/useSchedulesNew';

interface ScheduleDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate: Date | null;
  schedules: Schedule[];
}

export function ScheduleDetailModal({ isOpen, onClose, selectedDate, schedules }: ScheduleDetailModalProps) {
  const { deleteSchedule } = useSchedules();

  if (!selectedDate) return null;

  const dateString = selectedDate.toISOString().split('T')[0];
  const daySchedules = schedules.filter(schedule => schedule.shiftDate === dateString);

  const getShiftTypeColor = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'bg-yellow-100 text-yellow-800';
      case 'afternoon': return 'bg-blue-100 text-blue-800';
      case 'night': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'no_show': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Detail Jadwal - {formatDate(selectedDate)}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {daySchedules.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Tidak ada jadwal pada tanggal ini</p>
            </div>
          ) : (
            <div className="space-y-3">
              {daySchedules.map((schedule) => (
                <Card key={schedule.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <div>
                            <p className="font-medium">
                              {schedule.employee?.firstName} {schedule.employee?.lastName}
                            </p>
                            <p className="text-sm text-gray-600">
                              {schedule.employee?.department} - {schedule.employee?.position}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-gray-500" />
                          <span className="text-sm font-medium">
                            {schedule.startTime} - {schedule.endTime}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Badge className={getShiftTypeColor(schedule.shiftType)}>
                          {schedule.shiftType === 'morning' ? 'Pagi' : 
                           schedule.shiftType === 'afternoon' ? 'Siang' : 
                           schedule.shiftType === 'night' ? 'Malam' : schedule.shiftType}
                        </Badge>
                        <Badge className={getStatusColor(schedule.status)}>
                          {schedule.status === 'scheduled' ? 'Terjadwal' :
                           schedule.status === 'completed' ? 'Selesai' :
                           schedule.status === 'cancelled' ? 'Dibatalkan' :
                           schedule.status === 'no_show' ? 'Tidak Hadir' : schedule.status}
                        </Badge>
                        
                        <div className="flex space-x-1">
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => deleteSchedule(schedule.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    {schedule.notes && (
                      <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                        <strong>Catatan:</strong> {schedule.notes}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
