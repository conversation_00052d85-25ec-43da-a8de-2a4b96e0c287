Stack trace:
Frame         Function      Args
0007FFFFB730  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA630) msys-2.0.dll+0x1FE8E
0007FFFFB730  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA08) msys-2.0.dll+0x67F9
0007FFFFB730  000210046832 (000210286019, 0007FFFFB5E8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB730  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB730  000210068E24 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA10  00021006A225 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF89E410000 ntdll.dll
7FF89CD90000 KERNEL32.DLL
7FF89B6F0000 KERNELBASE.dll
7FF89D770000 USER32.dll
7FF89C070000 win32u.dll
7FF89D930000 GDI32.dll
7FF89BAD0000 gdi32full.dll
7FF89BC00000 msvcp_win.dll
7FF89BF50000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF89CEA0000 advapi32.dll
7FF89CCE0000 msvcrt.dll
7FF89D2D0000 sechost.dll
7FF89BCA0000 bcrypt.dll
7FF89D440000 RPCRT4.dll
7FF89AC20000 CRYPTBASE.DLL
7FF89B670000 bcryptPrimitives.dll
7FF89CE60000 IMM32.DLL
