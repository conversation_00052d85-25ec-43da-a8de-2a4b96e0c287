// Database services to replace Supabase queries
import { query, transaction, Employee, Schedule, LeaveRequest, Profile } from './database';

// Employee Services
export const employeeService = {
  // Get all employees
  async getAll(): Promise<Employee[]> {
    const result = await query(`
      SELECT * FROM employees 
      ORDER BY created_at DESC
    `);
    return result.rows;
  },

  // Get employee by ID
  async getById(id: string): Promise<Employee | null> {
    const result = await query(
      'SELECT * FROM employees WHERE id = $1',
      [id]
    );
    return result.rows[0] || null;
  },

  // Create employee
  async create(employeeData: Omit<Employee, 'id' | 'created_at' | 'updated_at'>): Promise<Employee> {
    const result = await query(`
      INSERT INTO employees (
        employee_id, first_name, last_name, email, phone, role, 
        department, position, join_date, status, shift, salary, 
        avatar, address, user_id, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      RETURNING *
    `, [
      employeeData.employee_id,
      employeeData.first_name,
      employeeData.last_name,
      employeeData.email,
      employeeData.phone,
      employeeData.role,
      employeeData.department,
      employeeData.position,
      employeeData.join_date,
      employeeData.status,
      employeeData.shift,
      employeeData.salary,
      employeeData.avatar,
      employeeData.address,
      employeeData.user_id,
      employeeData.created_by
    ]);
    return result.rows[0];
  },

  // Update employee
  async update(id: string, employeeData: Partial<Employee>): Promise<Employee> {
    const fields = Object.keys(employeeData).filter(key => key !== 'id');
    const values = fields.map(field => employeeData[field as keyof Employee]);
    const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

    const result = await query(`
      UPDATE employees 
      SET ${setClause}, updated_at = NOW()
      WHERE id = $1
      RETURNING *
    `, [id, ...values]);
    
    return result.rows[0];
  },

  // Delete employee
  async delete(id: string): Promise<void> {
    await query('DELETE FROM employees WHERE id = $1', [id]);
  }
};

// Schedule Services
export const scheduleService = {
  // Get all schedules with employee info
  async getAll(): Promise<any[]> {
    const result = await query(`
      SELECT s.*, 
             e.first_name, e.last_name, e.department, e.position
      FROM schedules s
      INNER JOIN employees e ON s.employee_id = e.id
      ORDER BY s.shift_date ASC
    `);
    return result.rows;
  },

  // Create schedule
  async create(scheduleData: Omit<Schedule, 'id' | 'created_at' | 'updated_at'>): Promise<Schedule> {
    const result = await query(`
      INSERT INTO schedules (
        employee_id, shift_date, shift_type, start_time, 
        end_time, status, notes, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [
      scheduleData.employee_id,
      scheduleData.shift_date,
      scheduleData.shift_type,
      scheduleData.start_time,
      scheduleData.end_time,
      scheduleData.status,
      scheduleData.notes,
      scheduleData.created_by
    ]);
    return result.rows[0];
  },

  // Update schedule
  async update(id: string, scheduleData: Partial<Schedule>): Promise<Schedule> {
    const fields = Object.keys(scheduleData).filter(key => key !== 'id');
    const values = fields.map(field => scheduleData[field as keyof Schedule]);
    const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

    const result = await query(`
      UPDATE schedules 
      SET ${setClause}, updated_at = NOW()
      WHERE id = $1
      RETURNING *
    `, [id, ...values]);
    
    return result.rows[0];
  },

  // Delete schedule
  async delete(id: string): Promise<void> {
    await query('DELETE FROM schedules WHERE id = $1', [id]);
  }
};

// Leave Request Services
export const leaveRequestService = {
  // Get all leave requests with employee info
  async getAll(): Promise<any[]> {
    const result = await query(`
      SELECT lr.*, 
             e.first_name, e.last_name, e.department, e.position
      FROM leave_requests lr
      INNER JOIN employees e ON lr.employee_id = e.id
      ORDER BY lr.created_at DESC
    `);
    return result.rows;
  },

  // Create leave request
  async create(leaveData: Omit<LeaveRequest, 'id' | 'created_at' | 'updated_at'>): Promise<LeaveRequest> {
    const result = await query(`
      INSERT INTO leave_requests (
        employee_id, leave_type, start_date, end_date, 
        reason, status, approved_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      leaveData.employee_id,
      leaveData.leave_type,
      leaveData.start_date,
      leaveData.end_date,
      leaveData.reason,
      leaveData.status,
      leaveData.approved_by
    ]);
    return result.rows[0];
  },

  // Update leave request
  async update(id: string, leaveData: Partial<LeaveRequest>): Promise<LeaveRequest> {
    const fields = Object.keys(leaveData).filter(key => key !== 'id');
    const values = fields.map(field => leaveData[field as keyof LeaveRequest]);
    const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

    const result = await query(`
      UPDATE leave_requests 
      SET ${setClause}, updated_at = NOW()
      WHERE id = $1
      RETURNING *
    `, [id, ...values]);
    
    return result.rows[0];
  }
};

// Profile Services
export const profileService = {
  // Get profile by user ID
  async getByUserId(userId: string): Promise<Profile | null> {
    const result = await query(
      'SELECT * FROM profiles WHERE id = $1',
      [userId]
    );
    return result.rows[0] || null;
  },

  // Update profile
  async update(userId: string, profileData: Partial<Profile>): Promise<Profile> {
    const fields = Object.keys(profileData).filter(key => key !== 'id');
    const values = fields.map(field => profileData[field as keyof Profile]);
    const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

    const result = await query(`
      UPDATE profiles 
      SET ${setClause}, updated_at = NOW()
      WHERE id = $1
      RETURNING *
    `, [userId, ...values]);
    
    return result.rows[0];
  }
};
