
import { Employee } from '../types/employee';

export const mockEmployees: Employee[] = [
  {
    id: '1',
    employeeId: 'DOC001',
    firstName: 'Dr. <PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+62 812-3456-7890',
    role: 'doctor',
    department: 'cardiology',
    position: 'Cardiologist',
    joinDate: '2020-01-15',
    status: 'active',
    shift: 'morning',
    salary: 25000000,
    certifications: ['Cardiology Specialist', 'Emergency Medicine'],
    skills: ['ECG Interpretation', 'Cardiac Catheterization']
  },
  {
    id: '2',
    employeeId: 'NUR001',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+62 813-4567-8901',
    role: 'nurse',
    department: 'emergency',
    position: 'Head Nurse',
    joinDate: '2019-03-20',
    status: 'active',
    shift: 'night',
    salary: 8000000,
    certifications: ['BLS', 'ACLS'],
    skills: ['Patient Care', 'Emergency Response']
  },
  {
    id: '3',
    employeeId: 'PHA001',
    firstName: '<PERSON>',
    lastName: 'Rizki',
    email: '<EMAIL>',
    phone: '+62 814-5678-9012',
    role: 'pharmacist',
    department: 'pharmacy',
    position: 'Senior Pharmacist',
    joinDate: '2021-06-10',
    status: 'active',
    shift: 'morning',
    salary: 12000000,
    certifications: ['Pharmacy License', 'Clinical Pharmacy'],
    skills: ['Drug Dispensing', 'Patient Counseling']
  },
  {
    id: '4',
    employeeId: 'DOC002',
    firstName: 'Dr. Michael',
    lastName: 'Chen',
    email: '<EMAIL>',
    phone: '+62 815-6789-0123',
    role: 'doctor',
    department: 'surgery',
    position: 'Surgeon',
    joinDate: '2018-11-05',
    status: 'active',
    shift: 'rotating',
    salary: 30000000,
    certifications: ['General Surgery', 'Laparoscopic Surgery'],
    skills: ['Surgical Procedures', 'Patient Assessment']
  },
  {
    id: '5',
    employeeId: 'NUR002',
    firstName: 'Siti',
    lastName: 'Nurhaliza',
    email: '<EMAIL>',
    phone: '+62 816-7890-1234',
    role: 'nurse',
    department: 'pediatrics',
    position: 'Pediatric Nurse',
    joinDate: '2022-01-12',
    status: 'active',
    shift: 'afternoon',
    salary: 7000000,
    certifications: ['Pediatric Nursing', 'BLS'],
    skills: ['Child Care', 'Family Communication']
  },
  {
    id: '6',
    employeeId: 'TEC001',
    firstName: 'David',
    lastName: 'Kurniawan',
    email: '<EMAIL>',
    phone: '+62 817-8901-2345',
    role: 'technician',
    department: 'laboratory',
    position: 'Lab Technician',
    joinDate: '2020-08-18',
    status: 'active',
    shift: 'morning',
    salary: 6000000,
    certifications: ['Medical Technology', 'Laboratory Safety'],
    skills: ['Lab Testing', 'Equipment Maintenance']
  }
];
