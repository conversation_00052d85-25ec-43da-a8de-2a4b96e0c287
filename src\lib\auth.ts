// Authentication service to replace <PERSON><PERSON><PERSON> Auth
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { query, transaction, User, Profile } from './database';

const JWT_SECRET = import.meta.env.VITE_JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = import.meta.env.VITE_JWT_EXPIRES_IN || '7d';

export interface AuthUser {
  id: string;
  email: string;
  email_confirmed: boolean;
  profile?: Profile;
}

export interface Session {
  access_token: string;
  user: AuthUser;
  expires_at: number;
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 10;
  return bcrypt.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

// Generate JWT token
export function generateToken(userId: string): string {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

// Verify JWT token
export function verifyToken(token: string): { userId: string } | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };
    return decoded;
  } catch (error) {
    return null;
  }
}

// Sign up user
export async function signUp(
  email: string, 
  password: string, 
  firstName?: string, 
  lastName?: string, 
  role?: string
): Promise<{ user?: AuthUser; error?: string }> {
  try {
    // Check if user already exists
    const existingUser = await query(
      'SELECT id FROM users WHERE email = $1',
      [email]
    );

    if (existingUser.rows.length > 0) {
      return { error: 'User already exists' };
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user and profile in transaction
    const result = await transaction(async (client) => {
      // Create user
      const userResult = await client.query(
        `INSERT INTO users (email, password_hash, email_confirmed, created_at, updated_at)
         VALUES ($1, $2, $3, NOW(), NOW())
         RETURNING id, email, email_confirmed, created_at, updated_at`,
        [email, passwordHash, false]
      );

      const user = userResult.rows[0];

      // Create profile
      const validRole = role || 'nurse';
      await client.query(
        `INSERT INTO profiles (id, first_name, last_name, email, role, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, NOW(), NOW())`,
        [user.id, firstName, lastName, email, validRole]
      );

      return user;
    });

    const authUser: AuthUser = {
      id: result.id,
      email: result.email,
      email_confirmed: result.email_confirmed,
    };

    return { user: authUser };
  } catch (error) {
    console.error('Sign up error:', error);
    return { error: 'Failed to create user' };
  }
}

// Sign in user
export async function signIn(
  email: string, 
  password: string
): Promise<{ session?: Session; error?: string }> {
  try {
    // Get user with profile
    const result = await query(
      `SELECT u.id, u.email, u.password_hash, u.email_confirmed,
              p.first_name, p.last_name, p.role
       FROM users u
       LEFT JOIN profiles p ON u.id = p.id
       WHERE u.email = $1`,
      [email]
    );

    if (result.rows.length === 0) {
      return { error: 'Invalid email or password' };
    }

    const user = result.rows[0];

    // Verify password
    const isValidPassword = await verifyPassword(password, user.password_hash);
    if (!isValidPassword) {
      return { error: 'Invalid email or password' };
    }

    // Generate token
    const token = generateToken(user.id);
    const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days

    const authUser: AuthUser = {
      id: user.id,
      email: user.email,
      email_confirmed: user.email_confirmed,
      profile: {
        id: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        role: user.role,
        created_at: new Date(),
        updated_at: new Date(),
      },
    };

    const session: Session = {
      access_token: token,
      user: authUser,
      expires_at: expiresAt,
    };

    return { session };
  } catch (error) {
    console.error('Sign in error:', error);
    return { error: 'Failed to sign in' };
  }
}

// Get user by token
export async function getUserByToken(token: string): Promise<AuthUser | null> {
  try {
    const decoded = verifyToken(token);
    if (!decoded) return null;

    const result = await query(
      `SELECT u.id, u.email, u.email_confirmed,
              p.first_name, p.last_name, p.role
       FROM users u
       LEFT JOIN profiles p ON u.id = p.id
       WHERE u.id = $1`,
      [decoded.userId]
    );

    if (result.rows.length === 0) return null;

    const user = result.rows[0];
    return {
      id: user.id,
      email: user.email,
      email_confirmed: user.email_confirmed,
      profile: {
        id: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        role: user.role,
        created_at: new Date(),
        updated_at: new Date(),
      },
    };
  } catch (error) {
    console.error('Get user by token error:', error);
    return null;
  }
}

// Token storage helpers (for client-side)
export const tokenStorage = {
  set: (token: string) => {
    localStorage.setItem('auth_token', token);
  },
  get: () => {
    return localStorage.getItem('auth_token');
  },
  remove: () => {
    localStorage.removeItem('auth_token');
  },
};
