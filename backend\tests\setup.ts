// Test setup file
import { Pool } from 'pg';
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Test database configuration
const testDbConfig = {
  host: process.env.TEST_DB_HOST || 'localhost',
  port: parseInt(process.env.TEST_DB_PORT || '5432'),
  database: process.env.TEST_DB_NAME || 'manajemen_karyawan_test',
  user: process.env.TEST_DB_USER || 'postgres',
  password: process.env.TEST_DB_PASSWORD || '',
  ssl: false,
  max: 5,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

let testPool: Pool;

// Setup test database
beforeAll(async () => {
  testPool = new Pool(testDbConfig);
  
  // Create test tables
  await setupTestDatabase();
});

// Cleanup after all tests
afterAll(async () => {
  if (testPool) {
    await testPool.end();
  }
});

// Clean up after each test
afterEach(async () => {
  if (testPool) {
    // Clean up test data
    await cleanupTestData();
  }
});

async function setupTestDatabase() {
  try {
    // Create test tables (simplified version)
    await testPool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
        email text UNIQUE NOT NULL,
        password_hash text NOT NULL,
        email_confirmed boolean DEFAULT false,
        suspended boolean DEFAULT false,
        failed_login_attempts integer DEFAULT 0,
        locked_until timestamp with time zone,
        last_login timestamp with time zone,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_at timestamp with time zone DEFAULT now() NOT NULL
      );
    `);

    await testPool.query(`
      CREATE TABLE IF NOT EXISTS profiles (
        id uuid NOT NULL PRIMARY KEY,
        first_name text,
        last_name text,
        email text,
        role text DEFAULT 'nurse',
        created_at timestamp with time zone DEFAULT now(),
        updated_at timestamp with time zone DEFAULT now(),
        FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);

    await testPool.query(`
      CREATE TABLE IF NOT EXISTS password_resets (
        id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
        user_id uuid NOT NULL,
        token_hash text NOT NULL,
        expires_at timestamp with time zone NOT NULL,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE(user_id)
      );
    `);

    await testPool.query(`
      CREATE TABLE IF NOT EXISTS email_verifications (
        id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
        user_id uuid NOT NULL,
        token_hash text NOT NULL,
        expires_at timestamp with time zone NOT NULL,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE(user_id)
      );
    `);

    await testPool.query(`
      CREATE TABLE IF NOT EXISTS user_activity_logs (
        id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
        user_id uuid,
        action text NOT NULL,
        resource text,
        resource_id uuid,
        ip_address inet,
        user_agent text,
        metadata jsonb,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      );
    `);

    console.log('✅ Test database setup completed');
  } catch (error) {
    console.error('❌ Test database setup failed:', error);
    throw error;
  }
}

async function cleanupTestData() {
  try {
    // Clean up in reverse order of dependencies
    await testPool.query('DELETE FROM user_activity_logs');
    await testPool.query('DELETE FROM email_verifications');
    await testPool.query('DELETE FROM password_resets');
    await testPool.query('DELETE FROM profiles');
    await testPool.query('DELETE FROM users');
  } catch (error) {
    console.error('Test cleanup error:', error);
  }
}

// Export test pool for use in tests
export { testPool };

// Test utilities
export const createTestUser = async (overrides: any = {}) => {
  const defaultUser = {
    email: '<EMAIL>',
    password_hash: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIoO', // 'password123'
    email_confirmed: true,
    first_name: 'Test',
    last_name: 'User',
    role: 'nurse'
  };

  const userData = { ...defaultUser, ...overrides };

  const userResult = await testPool.query(
    `INSERT INTO users (email, password_hash, email_confirmed, created_at, updated_at)
     VALUES ($1, $2, $3, NOW(), NOW())
     RETURNING *`,
    [userData.email, userData.password_hash, userData.email_confirmed]
  );

  const user = userResult.rows[0];

  await testPool.query(
    `INSERT INTO profiles (id, first_name, last_name, email, role, created_at, updated_at)
     VALUES ($1, $2, $3, $4, $5, NOW(), NOW())`,
    [user.id, userData.first_name, userData.last_name, userData.email, userData.role]
  );

  return user;
};

export const createTestAdmin = async () => {
  return createTestUser({
    email: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'User',
    role: 'admin'
  });
};

// Mock JWT for testing
export const mockJWT = {
  sign: jest.fn().mockReturnValue('mock-jwt-token'),
  verify: jest.fn().mockReturnValue({ userId: 'mock-user-id' }),
};

// Global test constants
export const TEST_CONSTANTS = {
  VALID_EMAIL: '<EMAIL>',
  VALID_PASSWORD: 'TestPassword123!',
  INVALID_EMAIL: 'invalid-email',
  WEAK_PASSWORD: '123',
  STRONG_PASSWORD: 'StrongPassword123!@#',
} as const;
