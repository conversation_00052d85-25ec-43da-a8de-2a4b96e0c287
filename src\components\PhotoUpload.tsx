
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Upload, Camera, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PhotoUploadProps {
  currentPhoto?: string;
  employeeName: string;
  onPhotoUpdate: (photoUrl: string) => void;
}

export function PhotoUpload({ currentPhoto, employeeName, onPhotoUpdate }: PhotoUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Error",
        description: "Please select a valid image file",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "Error",
        description: "File size must be less than 5MB",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    try {
      // For MVP, we'll use a simple file reader to convert to base64
      // In production, you'd upload to Supabase Storage
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUrl = e.target?.result as string;
        onPhotoUpdate(dataUrl);
        toast({
          title: "Success",
          description: "Photo uploaded successfully",
        });
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading photo:', error);
      toast({
        title: "Error",
        description: "Failed to upload photo",
        variant: "destructive",
      });
      setIsUploading(false);
    }
  };

  const handleRemovePhoto = () => {
    onPhotoUpdate('');
    toast({
      title: "Success",
      description: "Photo removed successfully",
    });
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      <Avatar className="w-32 h-32">
        <AvatarImage src={currentPhoto} alt={employeeName} />
        <AvatarFallback className="text-2xl">
          {employeeName.split(' ').map(n => n[0]).join('').toUpperCase()}
        </AvatarFallback>
      </Avatar>
      
      <div className="flex space-x-2">
        <Button
          variant="outline"
          size="sm"
          disabled={isUploading}
          onClick={() => document.getElementById('photo-upload')?.click()}
        >
          <Camera className="w-4 h-4 mr-2" />
          {isUploading ? 'Uploading...' : 'Change Photo'}
        </Button>
        
        {currentPhoto && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRemovePhoto}
          >
            <X className="w-4 h-4 mr-2" />
            Remove
          </Button>
        )}
      </div>

      <Input
        id="photo-upload"
        type="file"
        accept="image/*"
        className="hidden"
        onChange={handleFileUpload}
      />
    </div>
  );
}
