// Leave requests hook using API endpoints
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { leaveRequestApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { LeaveRequest } from '@/types/schedule';

// Transform API response to frontend format
const transformLeaveRequestFromApi = (apiLeaveRequest: any): LeaveRequest => ({
  id: apiLeaveRequest.id,
  employeeId: apiLeaveRequest.employee_id,
  startDate: apiLeaveRequest.start_date,
  endDate: apiLeaveRequest.end_date,
  leaveType: apiLeaveRequest.leave_type,
  reason: apiLeaveRequest.reason,
  status: apiLeaveRequest.status,
  approvedBy: apiLeaveRequest.approved_by,
  createdAt: apiLeaveRequest.created_at,
  updatedAt: apiLeaveRequest.updated_at,
  employee: apiLeaveRequest.employee ? {
    firstName: apiLeaveRequest.employee.first_name,
    lastName: apiLeaveRequest.employee.last_name,
    department: apiLeaveRequest.employee.department,
    position: apiLeaveRequest.employee.position,
  } : undefined,
});

// Transform frontend data to API format
const transformLeaveRequestToApi = (leaveRequest: Partial<LeaveRequest>) => ({
  employeeId: leaveRequest.employeeId,
  startDate: leaveRequest.startDate,
  endDate: leaveRequest.endDate,
  leaveType: leaveRequest.leaveType,
  reason: leaveRequest.reason,
  status: leaveRequest.status,
});

export function useLeaveRequests(params?: {
  employeeId?: string;
  status?: string;
}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all leave requests
  const { data: leaveRequestsData, isLoading, error } = useQuery({
    queryKey: ['leave-requests', params],
    queryFn: () => leaveRequestApi.getAll(params),
  });

  const leaveRequests = (leaveRequestsData?.leaveRequests || []).map(transformLeaveRequestFromApi);

  // Create leave request mutation
  const createLeaveRequestMutation = useMutation({
    mutationFn: async (leaveRequestData: Partial<LeaveRequest>) => {
      const apiData = transformLeaveRequestToApi(leaveRequestData);
      const response = await leaveRequestApi.create(apiData);
      return transformLeaveRequestFromApi(response.leaveRequest);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil diajukan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal mengajukan permohonan cuti",
        variant: "destructive",
      });
    },
  });

  // Update leave request mutation
  const updateLeaveRequestMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<LeaveRequest> }) => {
      const apiData = transformLeaveRequestToApi(data);
      const response = await leaveRequestApi.update(id, apiData);
      return transformLeaveRequestFromApi(response.leaveRequest);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui permohonan cuti",
        variant: "destructive",
      });
    },
  });

  // Delete leave request mutation
  const deleteLeaveRequestMutation = useMutation({
    mutationFn: async (id: string) => {
      await leaveRequestApi.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil dihapus",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menghapus permohonan cuti",
        variant: "destructive",
      });
    },
  });

  // Approve leave request mutation
  const approveLeaveRequestMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await leaveRequestApi.approve(id);
      return response.leaveRequest;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil disetujui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menyetujui permohonan cuti",
        variant: "destructive",
      });
    },
  });

  // Reject leave request mutation
  const rejectLeaveRequestMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await leaveRequestApi.reject(id);
      return response.leaveRequest;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil ditolak",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menolak permohonan cuti",
        variant: "destructive",
      });
    },
  });

  return {
    leaveRequests,
    isLoading,
    error,
    createLeaveRequest: createLeaveRequestMutation.mutate,
    updateLeaveRequest: updateLeaveRequestMutation.mutate,
    deleteLeaveRequest: deleteLeaveRequestMutation.mutate,
    approveLeaveRequest: approveLeaveRequestMutation.mutate,
    rejectLeaveRequest: rejectLeaveRequestMutation.mutate,
    isCreating: createLeaveRequestMutation.isPending,
    isUpdating: updateLeaveRequestMutation.isPending,
    isDeleting: deleteLeaveRequestMutation.isPending,
    isApproving: approveLeaveRequestMutation.isPending,
    isRejecting: rejectLeaveRequestMutation.isPending,
  };
}

// Hook for getting a single leave request
export function useLeaveRequest(id: string) {
  return useQuery({
    queryKey: ['leave-request', id],
    queryFn: async () => {
      const response = await leaveRequestApi.getById(id);
      return transformLeaveRequestFromApi(response.leaveRequest);
    },
    enabled: !!id,
  });
}

// Hook for leave request statistics
export function useLeaveRequestStatistics() {
  return useQuery({
    queryKey: ['leave-request-statistics'],
    queryFn: async () => {
      return await leaveRequestApi.getStatistics();
    },
  });
}
