import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { UserList } from '@/components/Management/UserList';
import { UserStats } from '@/components/Management/UserStats';
import { BulkUserOperations } from '@/components/Management/BulkUserOperations';
import { ActivityLogs } from '@/components/Management/ActivityLogs';
import { Users, BarChart3, Settings, Activity } from 'lucide-react';

export default function UserManagement() {
  const [activeTab, setActiveTab] = useState('users');

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Manajemen Pengguna</h1>
        <p className="text-gray-600">Kelola akun pengguna, peran, dan aktivitas sistem</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Daftar Pengguna
          </TabsTrigger>
          <TabsTrigger value="stats" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Statistik
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Operasi Bulk
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Log Aktivitas
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          <UserList />
        </TabsContent>

        <TabsContent value="stats" className="space-y-6">
          <UserStats />
        </TabsContent>

        <TabsContent value="bulk" className="space-y-6">
          <BulkUserOperations />
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <ActivityLogs />
        </TabsContent>
      </Tabs>
    </div>
  );
}
