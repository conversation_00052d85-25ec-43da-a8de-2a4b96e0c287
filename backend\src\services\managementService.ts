// Management service for departments, roles, and positions
import { query, Department, Role, Position } from '@/config/database';

export class ManagementService {
  // Department methods
  static async getAllDepartments(): Promise<Department[]> {
    try {
      const result = await query(`
        SELECT d.*,
               e.first_name as manager_first_name,
               e.last_name as manager_last_name
        FROM departments d
        LEFT JOIN employees e ON d.manager_id = e.id
        WHERE d.is_active = true
        ORDER BY d.name
      `);
      return result.rows;
    } catch (error) {
      console.error('Get all departments error:', error);
      throw new Error('Failed to fetch departments');
    }
  }

  static async getDepartmentById(id: string): Promise<Department | null> {
    try {
      const result = await query(
        'SELECT * FROM departments WHERE id = $1',
        [id]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Get department by ID error:', error);
      throw new Error('Failed to fetch department');
    }
  }

  static async createDepartment(departmentData: Omit<Department, 'id' | 'created_at' | 'updated_at'>): Promise<Department> {
    try {
      // Check if department name already exists
      const existingDept = await query(
        'SELECT id FROM departments WHERE name = $1 AND is_active = true',
        [departmentData.name]
      );
      if (existingDept.rows.length > 0) {
        throw new Error('Department name already exists');
      }

      const result = await query(`
        INSERT INTO departments (name, description, manager_id, is_active)
        VALUES ($1, $2, $3, $4)
        RETURNING *
      `, [
        departmentData.name,
        departmentData.description,
        departmentData.manager_id,
        departmentData.is_active !== false
      ]);

      return result.rows[0];
    } catch (error) {
      console.error('Create department error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create department');
    }
  }

  static async updateDepartment(id: string, departmentData: Partial<Department>): Promise<Department> {
    try {
      const { id: _, created_at, updated_at, ...updateData } = departmentData;

      const fields = Object.keys(updateData);
      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      // Check if department exists
      const existingDept = await this.getDepartmentById(id);
      if (!existingDept) {
        throw new Error('Department not found');
      }

      // Check if name is being updated and if it already exists
      if (updateData.name && updateData.name !== existingDept.name) {
        const nameCheck = await query(
          'SELECT id FROM departments WHERE name = $1 AND id != $2 AND is_active = true',
          [updateData.name, id]
        );
        if (nameCheck.rows.length > 0) {
          throw new Error('Department name already exists');
        }
      }

      const values = fields.map(field => updateData[field as keyof Department]);
      const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

      const result = await query(`
        UPDATE departments
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `, [id, ...values]);

      return result.rows[0];
    } catch (error) {
      console.error('Update department error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update department');
    }
  }

  static async deleteDepartment(id: string): Promise<void> {
    try {
      // Soft delete by setting is_active to false
      const result = await query(
        'UPDATE departments SET is_active = false, updated_at = NOW() WHERE id = $1',
        [id]
      );

      if (result.rowCount === 0) {
        throw new Error('Department not found');
      }
    } catch (error) {
      console.error('Delete department error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete department');
    }
  }

  // Role methods
  static async getAllRoles(): Promise<Role[]> {
    try {
      const result = await query(`
        SELECT * FROM roles 
        WHERE is_active = true
        ORDER BY name
      `);
      return result.rows;
    } catch (error) {
      console.error('Get all roles error:', error);
      throw new Error('Failed to fetch roles');
    }
  }

  static async getRoleById(id: string): Promise<Role | null> {
    try {
      const result = await query(
        'SELECT * FROM roles WHERE id = $1',
        [id]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Get role by ID error:', error);
      throw new Error('Failed to fetch role');
    }
  }

  static async createRole(roleData: Omit<Role, 'id' | 'created_at' | 'updated_at'>): Promise<Role> {
    try {
      // Check if role name already exists
      const existingRole = await query(
        'SELECT id FROM roles WHERE name = $1 AND is_active = true',
        [roleData.name]
      );
      if (existingRole.rows.length > 0) {
        throw new Error('Role name already exists');
      }

      const result = await query(`
        INSERT INTO roles (name, description, permissions, is_active)
        VALUES ($1, $2, $3, $4)
        RETURNING *
      `, [
        roleData.name,
        roleData.description,
        roleData.permissions ? JSON.stringify(roleData.permissions) : null,
        roleData.is_active !== false
      ]);

      return result.rows[0];
    } catch (error) {
      console.error('Create role error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create role');
    }
  }

  static async updateRole(id: string, roleData: Partial<Role>): Promise<Role> {
    try {
      const { id: _, created_at, updated_at, ...updateData } = roleData;
      
      const fields = Object.keys(updateData);
      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      // Check if role exists
      const existingRole = await this.getRoleById(id);
      if (!existingRole) {
        throw new Error('Role not found');
      }

      // Check if name is being updated and if it already exists
      if (updateData.name && updateData.name !== existingRole.name) {
        const nameCheck = await query(
          'SELECT id FROM roles WHERE name = $1 AND id != $2 AND is_active = true',
          [updateData.name, id]
        );
        if (nameCheck.rows.length > 0) {
          throw new Error('Role name already exists');
        }
      }

      // Handle permissions JSON
      if (updateData.permissions) {
        updateData.permissions = JSON.stringify(updateData.permissions);
      }

      const values = fields.map(field => updateData[field as keyof Role]);
      const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

      const result = await query(`
        UPDATE roles 
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `, [id, ...values]);

      return result.rows[0];
    } catch (error) {
      console.error('Update role error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update role');
    }
  }

  // Position methods
  static async getAllPositions(): Promise<any[]> {
    try {
      const result = await query(`
        SELECT p.*, 
               d.name as department_name,
               r.name as role_name
        FROM positions p
        LEFT JOIN departments d ON p.department_id = d.id
        LEFT JOIN roles r ON p.role_id = r.id
        WHERE p.is_active = true
        ORDER BY p.name
      `);
      return result.rows;
    } catch (error) {
      console.error('Get all positions error:', error);
      throw new Error('Failed to fetch positions');
    }
  }

  static async getPositionById(id: string): Promise<Position | null> {
    try {
      const result = await query(
        'SELECT * FROM positions WHERE id = $1',
        [id]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Get position by ID error:', error);
      throw new Error('Failed to fetch position');
    }
  }

  static async createPosition(positionData: Omit<Position, 'id' | 'created_at' | 'updated_at'>): Promise<Position> {
    try {
      // Check if position name already exists
      const existingPosition = await query(
        'SELECT id FROM positions WHERE name = $1 AND is_active = true',
        [positionData.name]
      );
      if (existingPosition.rows.length > 0) {
        throw new Error('Position name already exists');
      }

      const result = await query(`
        INSERT INTO positions (name, description, department_id, role_id, is_active)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `, [
        positionData.name,
        positionData.description,
        positionData.department_id,
        positionData.role_id,
        positionData.is_active !== false
      ]);

      return result.rows[0];
    } catch (error) {
      console.error('Create position error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create position');
    }
  }

  static async updatePosition(id: string, positionData: Partial<Position>): Promise<Position> {
    try {
      const { id: _, created_at, updated_at, ...updateData } = positionData;
      
      const fields = Object.keys(updateData);
      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      // Check if position exists
      const existingPosition = await this.getPositionById(id);
      if (!existingPosition) {
        throw new Error('Position not found');
      }

      // Check if name is being updated and if it already exists
      if (updateData.name && updateData.name !== existingPosition.name) {
        const nameCheck = await query(
          'SELECT id FROM positions WHERE name = $1 AND id != $2 AND is_active = true',
          [updateData.name, id]
        );
        if (nameCheck.rows.length > 0) {
          throw new Error('Position name already exists');
        }
      }

      const values = fields.map(field => updateData[field as keyof Position]);
      const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

      const result = await query(`
        UPDATE positions 
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `, [id, ...values]);

      return result.rows[0];
    } catch (error) {
      console.error('Update position error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update position');
    }
  }
}
