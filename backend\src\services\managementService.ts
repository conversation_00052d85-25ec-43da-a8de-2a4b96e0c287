// Management service for departments, roles, positions, and users
import { query, transaction, Department, Role, Position, User, Profile } from '@/config/database';
import { AuthService } from './authService';

export class ManagementService {
  // Department methods
  static async getAllDepartments(): Promise<Department[]> {
    try {
      const result = await query(`
        SELECT d.*,
               e.first_name as manager_first_name,
               e.last_name as manager_last_name
        FROM departments d
        LEFT JOIN employees e ON d.manager_id = e.id
        WHERE d.is_active = true
        ORDER BY d.name
      `);
      return result.rows;
    } catch (error) {
      console.error('Get all departments error:', error);
      throw new Error('Failed to fetch departments');
    }
  }

  static async getDepartmentById(id: string): Promise<Department | null> {
    try {
      const result = await query(
        'SELECT * FROM departments WHERE id = $1',
        [id]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Get department by ID error:', error);
      throw new Error('Failed to fetch department');
    }
  }

  static async createDepartment(departmentData: Omit<Department, 'id' | 'created_at' | 'updated_at'>): Promise<Department> {
    try {
      // Check if department name already exists
      const existingDept = await query(
        'SELECT id FROM departments WHERE name = $1 AND is_active = true',
        [departmentData.name]
      );
      if (existingDept.rows.length > 0) {
        throw new Error('Department name already exists');
      }

      const result = await query(`
        INSERT INTO departments (name, description, manager_id, is_active)
        VALUES ($1, $2, $3, $4)
        RETURNING *
      `, [
        departmentData.name,
        departmentData.description,
        departmentData.manager_id,
        departmentData.is_active !== false
      ]);

      return result.rows[0];
    } catch (error) {
      console.error('Create department error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create department');
    }
  }

  static async updateDepartment(id: string, departmentData: Partial<Department>): Promise<Department> {
    try {
      const { id: _, created_at, updated_at, ...updateData } = departmentData;

      const fields = Object.keys(updateData);
      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      // Check if department exists
      const existingDept = await this.getDepartmentById(id);
      if (!existingDept) {
        throw new Error('Department not found');
      }

      // Check if name is being updated and if it already exists
      if (updateData.name && updateData.name !== existingDept.name) {
        const nameCheck = await query(
          'SELECT id FROM departments WHERE name = $1 AND id != $2 AND is_active = true',
          [updateData.name, id]
        );
        if (nameCheck.rows.length > 0) {
          throw new Error('Department name already exists');
        }
      }

      const values = fields.map(field => updateData[field as keyof Department]);
      const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

      const result = await query(`
        UPDATE departments
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `, [id, ...values]);

      return result.rows[0];
    } catch (error) {
      console.error('Update department error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update department');
    }
  }

  static async deleteDepartment(id: string): Promise<void> {
    try {
      // Soft delete by setting is_active to false
      const result = await query(
        'UPDATE departments SET is_active = false, updated_at = NOW() WHERE id = $1',
        [id]
      );

      if (result.rowCount === 0) {
        throw new Error('Department not found');
      }
    } catch (error) {
      console.error('Delete department error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete department');
    }
  }

  // Role methods
  static async getAllRoles(): Promise<Role[]> {
    try {
      const result = await query(`
        SELECT * FROM roles
        WHERE is_active = true
        ORDER BY name
      `);
      return result.rows;
    } catch (error) {
      console.error('Get all roles error:', error);
      throw new Error('Failed to fetch roles');
    }
  }

  static async getRoleById(id: string): Promise<Role | null> {
    try {
      const result = await query(`
        SELECT * FROM roles
        WHERE id = $1 AND is_active = true
      `, [id]);

      return result.rows[0] || null;
    } catch (error) {
      console.error('Get role by ID error:', error);
      throw new Error('Failed to fetch role');
    }
  }

  static async createRole(roleData: Omit<Role, 'id' | 'created_at' | 'updated_at'>): Promise<Role> {
    try {
      // Check if role name already exists
      const existingRole = await query(
        'SELECT id FROM roles WHERE name = $1 AND is_active = true',
        [roleData.name]
      );
      if (existingRole.rows.length > 0) {
        throw new Error('Role name already exists');
      }

      const result = await query(`
        INSERT INTO roles (name, description, permissions, is_active)
        VALUES ($1, $2, $3, $4)
        RETURNING *
      `, [
        roleData.name,
        roleData.description,
        roleData.permissions ? JSON.stringify(roleData.permissions) : null,
        roleData.is_active !== false
      ]);

      return result.rows[0];
    } catch (error) {
      console.error('Create role error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create role');
    }
  }

  static async updateRole(id: string, roleData: Partial<Role>): Promise<Role> {
    try {
      const { id: _, created_at, updated_at, ...updateData } = roleData;
      
      const fields = Object.keys(updateData);
      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      // Check if role exists
      const existingRole = await this.getRoleById(id);
      if (!existingRole) {
        throw new Error('Role not found');
      }

      // Check if name is being updated and if it already exists
      if (updateData.name && updateData.name !== existingRole.name) {
        const nameCheck = await query(
          'SELECT id FROM roles WHERE name = $1 AND id != $2 AND is_active = true',
          [updateData.name, id]
        );
        if (nameCheck.rows.length > 0) {
          throw new Error('Role name already exists');
        }
      }

      // Handle permissions JSON
      if (updateData.permissions) {
        updateData.permissions = JSON.stringify(updateData.permissions);
      }

      const values = fields.map(field => updateData[field as keyof Role]);
      const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

      const result = await query(`
        UPDATE roles 
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `, [id, ...values]);

      return result.rows[0];
    } catch (error) {
      console.error('Update role error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update role');
    }
  }

  // Position methods
  static async getAllPositions(): Promise<any[]> {
    try {
      const result = await query(`
        SELECT p.*, 
               d.name as department_name,
               r.name as role_name
        FROM positions p
        LEFT JOIN departments d ON p.department_id = d.id
        LEFT JOIN roles r ON p.role_id = r.id
        WHERE p.is_active = true
        ORDER BY p.name
      `);
      return result.rows;
    } catch (error) {
      console.error('Get all positions error:', error);
      throw new Error('Failed to fetch positions');
    }
  }

  static async getPositionById(id: string): Promise<Position | null> {
    try {
      const result = await query(
        'SELECT * FROM positions WHERE id = $1',
        [id]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Get position by ID error:', error);
      throw new Error('Failed to fetch position');
    }
  }

  static async createPosition(positionData: Omit<Position, 'id' | 'created_at' | 'updated_at'>): Promise<Position> {
    try {
      // Check if position name already exists
      const existingPosition = await query(
        'SELECT id FROM positions WHERE name = $1 AND is_active = true',
        [positionData.name]
      );
      if (existingPosition.rows.length > 0) {
        throw new Error('Position name already exists');
      }

      const result = await query(`
        INSERT INTO positions (name, description, department_id, role_id, is_active)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `, [
        positionData.name,
        positionData.description,
        positionData.department_id,
        positionData.role_id,
        positionData.is_active !== false
      ]);

      return result.rows[0];
    } catch (error) {
      console.error('Create position error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create position');
    }
  }

  static async updatePosition(id: string, positionData: Partial<Position>): Promise<Position> {
    try {
      const { id: _, created_at, updated_at, ...updateData } = positionData;
      
      const fields = Object.keys(updateData);
      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      // Check if position exists
      const existingPosition = await this.getPositionById(id);
      if (!existingPosition) {
        throw new Error('Position not found');
      }

      // Check if name is being updated and if it already exists
      if (updateData.name && updateData.name !== existingPosition.name) {
        const nameCheck = await query(
          'SELECT id FROM positions WHERE name = $1 AND id != $2 AND is_active = true',
          [updateData.name, id]
        );
        if (nameCheck.rows.length > 0) {
          throw new Error('Position name already exists');
        }
      }

      const values = fields.map(field => updateData[field as keyof Position]);
      const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

      const result = await query(`
        UPDATE positions 
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `, [id, ...values]);

      return result.rows[0];
    } catch (error) {
      console.error('Update position error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update position');
    }
  }

  // User Management Methods
  static async getAllUsers(options: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  } = {}): Promise<{ users: any[]; total: number; pagination: any }> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        role,
        status,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      const offset = (page - 1) * limit;
      const params: any[] = [];
      let paramIndex = 1;
      let whereConditions: string[] = [];

      // Build WHERE clause
      if (search) {
        whereConditions.push(`(u.email ILIKE $${paramIndex} OR p.first_name ILIKE $${paramIndex} OR p.last_name ILIKE $${paramIndex})`);
        params.push(`%${search}%`);
        paramIndex++;
      }

      if (role) {
        whereConditions.push(`p.role = $${paramIndex}`);
        params.push(role);
        paramIndex++;
      }

      if (status === 'active') {
        whereConditions.push(`u.email_confirmed = true AND (u.suspended IS NULL OR u.suspended = false)`);
      } else if (status === 'suspended') {
        whereConditions.push(`u.suspended = true`);
      } else if (status === 'pending') {
        whereConditions.push(`u.email_confirmed = false`);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // Get total count
      const countResult = await query(
        `SELECT COUNT(*) as total
         FROM users u
         LEFT JOIN profiles p ON u.id = p.id
         ${whereClause}`,
        params
      );

      const total = parseInt(countResult.rows[0].total);

      // Get users with pagination
      const validSortColumns = ['created_at', 'email', 'first_name', 'last_name', 'role'];
      const sortColumn = validSortColumns.includes(sortBy) ?
        (sortBy === 'first_name' || sortBy === 'last_name' || sortBy === 'role' ? `p.${sortBy}` : `u.${sortBy}`) :
        'u.created_at';

      params.push(limit, offset);
      const result = await query(
        `SELECT u.id, u.email, u.email_confirmed, u.suspended, u.failed_login_attempts,
                u.locked_until, u.last_login, u.created_at, u.updated_at,
                p.first_name, p.last_name, p.role,
                e.id as employee_id, e.employee_id as emp_number
         FROM users u
         LEFT JOIN profiles p ON u.id = p.id
         LEFT JOIN employees e ON u.id = e.user_id
         ${whereClause}
         ORDER BY ${sortColumn} ${sortOrder}
         LIMIT $${paramIndex++} OFFSET $${paramIndex++}`,
        params
      );

      return {
        users: result.rows,
        total,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      console.error('Get all users error:', error);
      throw new Error('Failed to fetch users');
    }
  }

  static async createUser(userData: {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
    role?: string;
    emailConfirmed?: boolean;
  }): Promise<{ user: any; error?: string }> {
    try {
      const result = await AuthService.register(
        userData.email,
        userData.password,
        userData.firstName,
        userData.lastName,
        userData.role
      );

      if (result.error) {
        return { user: null, error: result.error };
      }

      // If admin is creating the user, mark email as confirmed if specified
      if (userData.emailConfirmed) {
        await query(
          'UPDATE users SET email_confirmed = $1, updated_at = NOW() WHERE id = $2',
          [userData.emailConfirmed, result.user!.id]
        );
      }

      return { user: result.user };
    } catch (error) {
      console.error('Create user error:', error);
      return { user: null, error: 'Failed to create user' };
    }
  }

  static async bulkCreateUsers(usersData: Array<{
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
    role?: string;
  }>): Promise<{ created: any[]; errors: any[] }> {
    const created: any[] = [];
    const errors: any[] = [];

    for (const userData of usersData) {
      try {
        const result = await this.createUser(userData);
        if (result.error) {
          errors.push({ email: userData.email, error: result.error });
        } else {
          created.push(result.user);
        }
      } catch (error) {
        errors.push({ email: userData.email, error: 'Failed to create user' });
      }
    }

    return { created, errors };
  }

  static async updateUserStatus(userId: string, status: {
    suspended?: boolean;
    emailConfirmed?: boolean;
  }): Promise<void> {
    try {
      const updateFields: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      if (status.suspended !== undefined) {
        updateFields.push(`suspended = $${paramIndex++}`);
        params.push(status.suspended);
      }

      if (status.emailConfirmed !== undefined) {
        updateFields.push(`email_confirmed = $${paramIndex++}`);
        params.push(status.emailConfirmed);
      }

      if (updateFields.length === 0) {
        throw new Error('No status fields to update');
      }

      updateFields.push(`updated_at = NOW()`);
      params.push(userId);

      await query(
        `UPDATE users SET ${updateFields.join(', ')} WHERE id = $${paramIndex}`,
        params
      );
    } catch (error) {
      console.error('Update user status error:', error);
      throw new Error('Failed to update user status');
    }
  }

  static async bulkUpdateUsers(updates: Array<{
    id: string;
    suspended?: boolean;
    emailConfirmed?: boolean;
    role?: string;
  }>): Promise<{ updated: string[]; errors: any[] }> {
    const updated: string[] = [];
    const errors: any[] = [];

    for (const update of updates) {
      try {
        // Update user status
        if (update.suspended !== undefined || update.emailConfirmed !== undefined) {
          await this.updateUserStatus(update.id, {
            suspended: update.suspended,
            emailConfirmed: update.emailConfirmed
          });
        }

        // Update profile role
        if (update.role !== undefined) {
          await AuthService.updateProfile(update.id, { role: update.role as any });
        }

        updated.push(update.id);
      } catch (error) {
        errors.push({ id: update.id, error: 'Failed to update user' });
      }
    }

    return { updated, errors };
  }

  static async deleteUser(userId: string): Promise<void> {
    try {
      // Check if user exists
      const user = await AuthService.getUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Delete user (cascade will handle profile and related data)
      await query('DELETE FROM users WHERE id = $1', [userId]);
    } catch (error) {
      console.error('Delete user error:', error);
      throw new Error('Failed to delete user');
    }
  }

  static async bulkDeleteUsers(userIds: string[]): Promise<{ deleted: string[]; errors: any[] }> {
    const deleted: string[] = [];
    const errors: any[] = [];

    for (const userId of userIds) {
      try {
        await this.deleteUser(userId);
        deleted.push(userId);
      } catch (error) {
        errors.push({ id: userId, error: 'Failed to delete user' });
      }
    }

    return { deleted, errors };
  }

  static async getUserStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    suspendedUsers: number;
    pendingVerification: number;
    recentLogins: number;
    usersByRole: any[];
  }> {
    try {
      const stats = await Promise.all([
        query('SELECT COUNT(*) as total FROM users'),
        query('SELECT COUNT(*) as active FROM users WHERE email_confirmed = true AND (suspended IS NULL OR suspended = false)'),
        query('SELECT COUNT(*) as suspended FROM users WHERE suspended = true'),
        query('SELECT COUNT(*) as pending FROM users WHERE email_confirmed = false'),
        query(`SELECT COUNT(*) as recent FROM user_activity_logs
               WHERE action = 'login' AND created_at > NOW() - INTERVAL '24 hours'`),
        query(`SELECT p.role, COUNT(*) as count
               FROM profiles p
               JOIN users u ON p.id = u.id
               WHERE u.email_confirmed = true AND (u.suspended IS NULL OR u.suspended = false)
               GROUP BY p.role
               ORDER BY count DESC`)
      ]);

      return {
        totalUsers: parseInt(stats[0].rows[0].total),
        activeUsers: parseInt(stats[1].rows[0].active),
        suspendedUsers: parseInt(stats[2].rows[0].suspended),
        pendingVerification: parseInt(stats[3].rows[0].pending),
        recentLogins: parseInt(stats[4].rows[0].recent),
        usersByRole: stats[5].rows
      };
    } catch (error) {
      console.error('Get user stats error:', error);
      throw new Error('Failed to fetch user statistics');
    }
  }

  static async resetUserPassword(userId: string, newPassword: string): Promise<void> {
    try {
      const passwordHash = await AuthService.hashPassword(newPassword);
      await query(
        'UPDATE users SET password_hash = $1, updated_at = NOW() WHERE id = $2',
        [passwordHash, userId]
      );
    } catch (error) {
      console.error('Reset user password error:', error);
      throw new Error('Failed to reset user password');
    }
  }

  static async unlockUser(userId: string): Promise<void> {
    try {
      await query(
        'UPDATE users SET failed_login_attempts = 0, locked_until = NULL, updated_at = NOW() WHERE id = $1',
        [userId]
      );
    } catch (error) {
      console.error('Unlock user error:', error);
      throw new Error('Failed to unlock user');
    }
  }

  // Department Hierarchy Methods
  static async getDepartmentHierarchy(): Promise<any[]> {
    try {
      const result = await query(`
        WITH RECURSIVE department_tree AS (
          -- Base case: departments without parent
          SELECT
            d.id, d.name, d.description, d.manager_id, d.parent_id, d.is_active,
            p.first_name as manager_first_name, p.last_name as manager_last_name,
            0 as level,
            ARRAY[d.name] as path
          FROM departments d
          LEFT JOIN profiles p ON d.manager_id = p.id
          WHERE d.parent_id IS NULL AND d.is_active = true

          UNION ALL

          -- Recursive case: departments with parent
          SELECT
            d.id, d.name, d.description, d.manager_id, d.parent_id, d.is_active,
            p.first_name as manager_first_name, p.last_name as manager_last_name,
            dt.level + 1,
            dt.path || d.name
          FROM departments d
          LEFT JOIN profiles p ON d.manager_id = p.id
          JOIN department_tree dt ON d.parent_id = dt.id
          WHERE d.is_active = true
        )
        SELECT * FROM department_tree
        ORDER BY level, name
      `);

      return result.rows;
    } catch (error) {
      console.error('Get department hierarchy error:', error);
      throw new Error('Failed to fetch department hierarchy');
    }
  }

  static async getDepartmentWithSubdepartments(departmentId: string): Promise<any> {
    try {
      const result = await query(`
        WITH RECURSIVE department_tree AS (
          -- Base case: the requested department
          SELECT
            d.id, d.name, d.description, d.manager_id, d.parent_id, d.is_active,
            p.first_name as manager_first_name, p.last_name as manager_last_name,
            0 as level
          FROM departments d
          LEFT JOIN profiles p ON d.manager_id = p.id
          WHERE d.id = $1

          UNION ALL

          -- Recursive case: subdepartments
          SELECT
            d.id, d.name, d.description, d.manager_id, d.parent_id, d.is_active,
            p.first_name as manager_first_name, p.last_name as manager_last_name,
            dt.level + 1
          FROM departments d
          LEFT JOIN profiles p ON d.manager_id = p.id
          JOIN department_tree dt ON d.parent_id = dt.id
          WHERE d.is_active = true
        )
        SELECT * FROM department_tree
        ORDER BY level, name
      `, [departmentId]);

      return result.rows;
    } catch (error) {
      console.error('Get department with subdepartments error:', error);
      throw new Error('Failed to fetch department with subdepartments');
    }
  }

  static async assignDepartmentManager(departmentId: string, managerId: string): Promise<void> {
    try {
      // Verify manager exists and has manager role
      const managerCheck = await query(
        'SELECT p.role FROM profiles p JOIN users u ON p.id = u.id WHERE u.id = $1 AND u.email_confirmed = true',
        [managerId]
      );

      if (managerCheck.rows.length === 0) {
        throw new Error('Manager not found or not verified');
      }

      if (managerCheck.rows[0].role !== 'manager' && managerCheck.rows[0].role !== 'admin') {
        throw new Error('User must have manager or admin role');
      }

      await query(
        'UPDATE departments SET manager_id = $1, updated_at = NOW() WHERE id = $2',
        [managerId, departmentId]
      );
    } catch (error) {
      console.error('Assign department manager error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to assign department manager');
    }
  }

  static async removeDepartmentManager(departmentId: string): Promise<void> {
    try {
      await query(
        'UPDATE departments SET manager_id = NULL, updated_at = NOW() WHERE id = $1',
        [departmentId]
      );
    } catch (error) {
      console.error('Remove department manager error:', error);
      throw new Error('Failed to remove department manager');
    }
  }

  // Organizational Reporting Structure
  static async getOrganizationalChart(): Promise<any> {
    try {
      const result = await query(`
        WITH RECURSIVE org_chart AS (
          -- Top level: departments without parent
          SELECT
            d.id, d.name, d.description, d.manager_id, d.parent_id,
            p.first_name as manager_first_name, p.last_name as manager_last_name,
            p.role as manager_role,
            0 as level,
            'department' as type
          FROM departments d
          LEFT JOIN profiles p ON d.manager_id = p.id
          WHERE d.parent_id IS NULL AND d.is_active = true

          UNION ALL

          -- Recursive: subdepartments and employees
          SELECT
            d.id, d.name, d.description, d.manager_id, d.parent_id,
            p.first_name as manager_first_name, p.last_name as manager_last_name,
            p.role as manager_role,
            oc.level + 1,
            'department' as type
          FROM departments d
          LEFT JOIN profiles p ON d.manager_id = p.id
          JOIN org_chart oc ON d.parent_id = oc.id
          WHERE d.is_active = true
        )
        SELECT * FROM org_chart
        ORDER BY level, name
      `);

      // Get employees for each department
      const employees = await query(`
        SELECT
          e.id, e.employee_id, e.department_id, e.position_id,
          p.first_name, p.last_name, p.role,
          pos.name as position_name,
          d.name as department_name
        FROM employees e
        JOIN profiles p ON e.user_id = p.id
        JOIN users u ON p.id = u.id
        LEFT JOIN positions pos ON e.position_id = pos.id
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE u.email_confirmed = true AND (u.suspended IS NULL OR u.suspended = false)
        ORDER BY d.name, pos.name, p.first_name
      `);

      return {
        departments: result.rows,
        employees: employees.rows
      };
    } catch (error) {
      console.error('Get organizational chart error:', error);
      throw new Error('Failed to fetch organizational chart');
    }
  }

  static async getDepartmentEmployees(departmentId: string): Promise<any[]> {
    try {
      const result = await query(`
        SELECT
          e.id, e.employee_id, e.department_id, e.position_id,
          p.first_name, p.last_name, p.role,
          pos.name as position_name,
          u.email, u.email_confirmed, u.suspended,
          e.hire_date, e.employment_status
        FROM employees e
        JOIN profiles p ON e.user_id = p.id
        JOIN users u ON p.id = u.id
        LEFT JOIN positions pos ON e.position_id = pos.id
        WHERE e.department_id = $1
        ORDER BY pos.name, p.first_name
      `, [departmentId]);

      return result.rows;
    } catch (error) {
      console.error('Get department employees error:', error);
      throw new Error('Failed to fetch department employees');
    }
  }

  static async getManagerSubordinates(managerId: string): Promise<any[]> {
    try {
      const result = await query(`
        SELECT
          e.id, e.employee_id, e.department_id, e.position_id,
          p.first_name, p.last_name, p.role,
          pos.name as position_name,
          d.name as department_name,
          u.email, u.email_confirmed, u.suspended
        FROM employees e
        JOIN profiles p ON e.user_id = p.id
        JOIN users u ON p.id = u.id
        LEFT JOIN positions pos ON e.position_id = pos.id
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE d.manager_id = $1
        ORDER BY d.name, pos.name, p.first_name
      `, [managerId]);

      return result.rows;
    } catch (error) {
      console.error('Get manager subordinates error:', error);
      throw new Error('Failed to fetch manager subordinates');
    }
  }

  // Advanced Position Management
  static async getPositionHierarchy(): Promise<any[]> {
    try {
      const result = await query(`
        WITH RECURSIVE position_tree AS (
          -- Base case: positions without parent (top level)
          SELECT
            p.id, p.name, p.description, p.department_id, p.parent_position_id,
            p.salary_min, p.salary_max, p.is_active,
            d.name as department_name,
            0 as level,
            ARRAY[p.name] as path
          FROM positions p
          LEFT JOIN departments d ON p.department_id = d.id
          WHERE p.parent_position_id IS NULL AND p.is_active = true

          UNION ALL

          -- Recursive case: positions with parent
          SELECT
            p.id, p.name, p.description, p.department_id, p.parent_position_id,
            p.salary_min, p.salary_max, p.is_active,
            d.name as department_name,
            pt.level + 1,
            pt.path || p.name
          FROM positions p
          LEFT JOIN departments d ON p.department_id = d.id
          JOIN position_tree pt ON p.parent_position_id = pt.id
          WHERE p.is_active = true
        )
        SELECT * FROM position_tree
        ORDER BY level, department_name, name
      `);

      return result.rows;
    } catch (error) {
      console.error('Get position hierarchy error:', error);
      throw new Error('Failed to fetch position hierarchy');
    }
  }

  static async getPositionsByDepartment(departmentId: string): Promise<any[]> {
    try {
      const result = await query(`
        SELECT
          p.id, p.name, p.description, p.department_id, p.parent_position_id,
          p.salary_min, p.salary_max, p.is_active,
          parent.name as parent_position_name,
          COUNT(e.id) as employee_count
        FROM positions p
        LEFT JOIN positions parent ON p.parent_position_id = parent.id
        LEFT JOIN employees e ON p.id = e.position_id
        WHERE p.department_id = $1 AND p.is_active = true
        GROUP BY p.id, p.name, p.description, p.department_id, p.parent_position_id,
                 p.salary_min, p.salary_max, p.is_active, parent.name
        ORDER BY p.name
      `, [departmentId]);

      return result.rows;
    } catch (error) {
      console.error('Get positions by department error:', error);
      throw new Error('Failed to fetch positions by department');
    }
  }

  static async getPositionEmployees(positionId: string): Promise<any[]> {
    try {
      const result = await query(`
        SELECT
          e.id, e.employee_id, e.department_id, e.position_id,
          p.first_name, p.last_name, p.role,
          d.name as department_name,
          u.email, u.email_confirmed, u.suspended,
          e.hire_date, e.employment_status
        FROM employees e
        JOIN profiles p ON e.user_id = p.id
        JOIN users u ON p.id = u.id
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE e.position_id = $1
        ORDER BY p.first_name, p.last_name
      `, [positionId]);

      return result.rows;
    } catch (error) {
      console.error('Get position employees error:', error);
      throw new Error('Failed to fetch position employees');
    }
  }

  // Advanced Analytics and Reporting
  static async getOrganizationalStats(): Promise<any> {
    try {
      const stats = await Promise.all([
        // Department stats
        query(`
          SELECT
            COUNT(*) as total_departments,
            COUNT(CASE WHEN manager_id IS NOT NULL THEN 1 END) as departments_with_managers,
            COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as top_level_departments
          FROM departments WHERE is_active = true
        `),

        // Position stats
        query(`
          SELECT
            COUNT(*) as total_positions,
            COUNT(CASE WHEN parent_position_id IS NOT NULL THEN 1 END) as subordinate_positions,
            AVG(salary_max - salary_min) as avg_salary_range
          FROM positions WHERE is_active = true
        `),

        // Employee distribution
        query(`
          SELECT
            d.name as department_name,
            COUNT(e.id) as employee_count,
            COUNT(CASE WHEN p.role = 'manager' THEN 1 END) as manager_count
          FROM departments d
          LEFT JOIN employees e ON d.id = e.department_id
          LEFT JOIN profiles p ON e.user_id = p.id
          WHERE d.is_active = true
          GROUP BY d.id, d.name
          ORDER BY employee_count DESC
        `),

        // Role distribution
        query(`
          SELECT
            p.role,
            COUNT(*) as count
          FROM profiles p
          JOIN users u ON p.id = u.id
          WHERE u.email_confirmed = true AND (u.suspended IS NULL OR u.suspended = false)
          GROUP BY p.role
          ORDER BY count DESC
        `)
      ]);

      return {
        departmentStats: stats[0].rows[0],
        positionStats: stats[1].rows[0],
        employeeDistribution: stats[2].rows,
        roleDistribution: stats[3].rows
      };
    } catch (error) {
      console.error('Get organizational stats error:', error);
      throw new Error('Failed to fetch organizational statistics');
    }
  }

  static async searchEmployees(searchParams: {
    query?: string;
    department?: string;
    position?: string;
    role?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ employees: any[]; total: number }> {
    try {
      const {
        query: searchQuery,
        department,
        position,
        role,
        status,
        limit = 20,
        offset = 0
      } = searchParams;

      const conditions: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      if (searchQuery) {
        conditions.push(`(p.first_name ILIKE $${paramIndex} OR p.last_name ILIKE $${paramIndex} OR u.email ILIKE $${paramIndex} OR e.employee_id ILIKE $${paramIndex})`);
        params.push(`%${searchQuery}%`);
        paramIndex++;
      }

      if (department) {
        conditions.push(`d.id = $${paramIndex}`);
        params.push(department);
        paramIndex++;
      }

      if (position) {
        conditions.push(`pos.id = $${paramIndex}`);
        params.push(position);
        paramIndex++;
      }

      if (role) {
        conditions.push(`p.role = $${paramIndex}`);
        params.push(role);
        paramIndex++;
      }

      if (status === 'active') {
        conditions.push(`u.email_confirmed = true AND (u.suspended IS NULL OR u.suspended = false)`);
      } else if (status === 'suspended') {
        conditions.push(`u.suspended = true`);
      } else if (status === 'pending') {
        conditions.push(`u.email_confirmed = false`);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Get total count
      const countResult = await query(
        `SELECT COUNT(*) as total
         FROM employees e
         JOIN profiles p ON e.user_id = p.id
         JOIN users u ON p.id = u.id
         LEFT JOIN departments d ON e.department_id = d.id
         LEFT JOIN positions pos ON e.position_id = pos.id
         ${whereClause}`,
        params
      );

      const total = parseInt(countResult.rows[0].total);

      // Get employees with pagination
      params.push(limit, offset);
      const result = await query(
        `SELECT
           e.id, e.employee_id, e.department_id, e.position_id,
           p.first_name, p.last_name, p.role,
           d.name as department_name,
           pos.name as position_name,
           u.email, u.email_confirmed, u.suspended,
           e.hire_date, e.employment_status
         FROM employees e
         JOIN profiles p ON e.user_id = p.id
         JOIN users u ON p.id = u.id
         LEFT JOIN departments d ON e.department_id = d.id
         LEFT JOIN positions pos ON e.position_id = pos.id
         ${whereClause}
         ORDER BY p.first_name, p.last_name
         LIMIT $${paramIndex++} OFFSET $${paramIndex++}`,
        params
      );

      return {
        employees: result.rows,
        total
      };
    } catch (error) {
      console.error('Search employees error:', error);
      throw new Error('Failed to search employees');
    }
  }
}
