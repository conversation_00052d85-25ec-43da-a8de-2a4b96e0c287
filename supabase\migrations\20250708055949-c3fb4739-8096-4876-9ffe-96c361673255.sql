
-- Ensure employee_role enum exists and recreate handle_new_user function
DO $$ BEGIN
    CREATE TYPE employee_role AS ENUM (
        'admin',
        'doctor', 
        'nurse',
        'pharmacist',
        'technician',
        'receptionist',
        'manager'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update the handle_new_user function to handle role assignment properly
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, email, role)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data ->> 'first_name',
    NEW.raw_user_meta_data ->> 'last_name',
    NEW.email,
    COALESCE(
      (NEW.raw_user_meta_data ->> 'role')::employee_role,
      'nurse'::employee_role
    )
  );
  RETURN NEW;
END;
$$;
