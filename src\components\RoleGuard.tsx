
import { useRoleAccess } from '@/hooks/useRoleAccess';
import { EmployeeRole } from '@/types/employee';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: EmployeeRole[];
  fallback?: React.ReactNode;
}

export function RoleGuard({ children, allowedRoles, fallback = null }: RoleGuardProps) {
  const { hasAnyRole, isLoading } = useRoleAccess();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!hasAnyRole(allowedRoles)) {
    return fallback ? <>{fallback}</> : (
      <div className="p-6 text-center">
        <div className="text-gray-500">
          <h3 className="text-lg font-semibold mb-2"><PERSON><PERSON><PERSON></h3>
          <p>Anda tidak memiliki izin untuk mengakses halaman ini.</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
