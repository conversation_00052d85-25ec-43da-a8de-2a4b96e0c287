
import { useParams, useNavigate } from 'react-router-dom';
import { useEmployees } from '@/hooks/useEmployeesNew';
import EmployeeForm from '@/components/EmployeeForm';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { Card } from '@/components/ui/card';

export default function EditEmployee() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { employees, updateEmployee, isUpdating, isLoading } = useEmployees();

  const employee = employees.find(emp => emp.id === id);

  const handleSubmit = (data: any) => {
    if (employee) {
      updateEmployee({
        ...employee,
        ...data,
      });
      navigate(`/employees/${employee.id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="p-6">
        <Card className="p-6 text-center">
          <p className="text-gray-600">Karyawan tidak ditemukan</p>
          <Button onClick={() => navigate('/employees')} className="mt-4">
            Kembali ke Daftar Karyawan
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => navigate(`/employees/${employee.id}`)}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Kembali</span>
        </Button>
      </div>

      <div className="mb-6">
        <h1 className="text-2xl font-bold">Edit Karyawan</h1>
        <p className="text-gray-600">Perbarui informasi karyawan</p>
      </div>

      <EmployeeForm
        employee={employee}
        onSubmit={handleSubmit}
        onCancel={() => navigate(`/employees/${employee.id}`)}
        isLoading={isUpdating}
      />
    </div>
  );
}
