// Database connection and query utilities
import { Pool, PoolClient } from 'pg';

// Database configuration
const dbConfig = {
  host: import.meta.env.VITE_DB_HOST || 'localhost',
  port: parseInt(import.meta.env.VITE_DB_PORT || '5432'),
  database: import.meta.env.VITE_DB_NAME || 'manajemen_karyawan',
  user: import.meta.env.VITE_DB_USER || 'app_user',
  password: import.meta.env.VITE_DB_PASSWORD || '',
  ssl: import.meta.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

// Create connection pool
export const pool = new Pool(dbConfig);

// Database query helper
export async function query(text: string, params?: any[]): Promise<any> {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

// Transaction helper
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Database types (matching your schema)
export interface User {
  id: string;
  email: string;
  password_hash: string;
  email_confirmed: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Profile {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  role: 'admin' | 'doctor' | 'nurse' | 'pharmacist' | 'technician' | 'receptionist' | 'manager';
  created_at: Date;
  updated_at: Date;
}

export interface Employee {
  id: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'doctor' | 'nurse' | 'pharmacist' | 'technician' | 'receptionist' | 'manager';
  department: 'emergency' | 'surgery' | 'pediatrics' | 'cardiology' | 'orthopedics' | 'pharmacy' | 'laboratory' | 'radiology' | 'administration' | 'maintenance';
  position: string;
  join_date: Date;
  status: 'active' | 'inactive' | 'on_leave' | 'terminated';
  shift: 'morning' | 'afternoon' | 'night' | 'rotating' | 'regular';
  salary?: number;
  avatar?: string;
  address?: string;
  user_id?: string;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Schedule {
  id: string;
  employee_id: string;
  shift_date: Date;
  shift_type: 'morning' | 'afternoon' | 'night' | 'rotating' | 'regular';
  start_time: string;
  end_time: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  notes?: string;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
}

export interface LeaveRequest {
  id: string;
  employee_id: string;
  leave_type: string;
  start_date: Date;
  end_date: Date;
  reason?: string;
  status: string;
  approved_by?: string;
  created_at: Date;
  updated_at: Date;
}

// Helper function to get current user ID (you'll need to implement this based on your auth system)
export function getCurrentUserId(): string | null {
  // This will be implemented based on your authentication system
  // For now, return null - you'll need to integrate with your auth context
  return null;
}
