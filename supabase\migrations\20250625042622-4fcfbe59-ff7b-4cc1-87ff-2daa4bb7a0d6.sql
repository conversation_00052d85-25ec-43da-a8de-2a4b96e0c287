
-- Create enum types for better data consistency
CREATE TYPE employee_role AS ENUM ('admin', 'doctor', 'nurse', 'pharmacist', 'technician', 'receptionist', 'manager');
CREATE TYPE department_type AS ENUM ('emergency', 'surgery', 'pediatrics', 'cardiology', 'orthopedics', 'pharmacy', 'laboratory', 'radiology', 'administration', 'maintenance');
CREATE TYPE employee_status AS ENUM ('active', 'inactive', 'on_leave', 'terminated');
CREATE TYPE shift_type AS ENUM ('morning', 'afternoon', 'night', 'rotating');

-- Create profiles table for user management
CREATE TABLE public.profiles (
  id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  role employee_role DEFAULT 'admin',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (id)
);

-- Create employees table
CREATE TABLE public.employees (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id TEXT NOT NULL UNIQUE,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT,
  role employee_role NOT NULL,
  department department_type NOT NULL,
  position TEXT NOT NULL,
  join_date DATE NOT NULL,
  status employee_status NOT NULL DEFAULT 'active',
  shift shift_type NOT NULL,
  salary DECIMAL(12,2),
  avatar TEXT,
  address TEXT,
  emergency_contact JSONB,
  certifications TEXT[],
  skills TEXT[],
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;

-- Create trigger to automatically create profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, email)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data ->> 'first_name',
    NEW.raw_user_meta_data ->> 'last_name',
    NEW.email
  );
  RETURN NEW;
END;
$$;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- RLS Policies for profiles
CREATE POLICY "Users can view their own profile"
  ON public.profiles
  FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- RLS Policies for employees (authenticated users can manage employees)
CREATE POLICY "Authenticated users can view employees"
  ON public.employees
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can create employees"
  ON public.employees
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Authenticated users can update employees"
  ON public.employees
  FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can delete employees"
  ON public.employees
  FOR DELETE
  TO authenticated
  USING (true);

-- Insert sample data
INSERT INTO public.employees (
  employee_id, first_name, last_name, email, phone, role, department, 
  position, join_date, status, shift, salary, certifications, skills
) VALUES
  ('DOC001', 'Dr. Sarah', 'Johnson', '<EMAIL>', '+62 812-3456-7890', 'doctor', 'cardiology', 'Cardiologist', '2020-01-15', 'active', 'morning', 25000000, ARRAY['Cardiology Specialist', 'Emergency Medicine'], ARRAY['ECG Interpretation', 'Cardiac Catheterization']),
  ('NUR001', 'Maria', 'Santos', '<EMAIL>', '+62 813-4567-8901', 'nurse', 'emergency', 'Head Nurse', '2019-03-20', 'active', 'night', 8000000, ARRAY['BLS', 'ACLS'], ARRAY['Patient Care', 'Emergency Response']),
  ('PHA001', 'Ahmad', 'Rizki', '<EMAIL>', '+62 814-5678-9012', 'pharmacist', 'pharmacy', 'Senior Pharmacist', '2021-06-10', 'active', 'morning', 12000000, ARRAY['Pharmacy License', 'Clinical Pharmacy'], ARRAY['Drug Dispensing', 'Patient Counseling']),
  ('DOC002', 'Dr. Michael', 'Chen', '<EMAIL>', '+62 815-6789-0123', 'doctor', 'surgery', 'Surgeon', '2018-11-05', 'active', 'rotating', 30000000, ARRAY['General Surgery', 'Laparoscopic Surgery'], ARRAY['Surgical Procedures', 'Patient Assessment']),
  ('NUR002', 'Siti', 'Nurhaliza', '<EMAIL>', '+62 816-7890-1234', 'nurse', 'pediatrics', 'Pediatric Nurse', '2022-01-12', 'active', 'afternoon', 7000000, ARRAY['Pediatric Nursing', 'BLS'], ARRAY['Child Care', 'Family Communication']),
  ('TEC001', 'David', 'Kurniawan', '<EMAIL>', '+62 817-8901-2345', 'technician', 'laboratory', 'Lab Technician', '2020-08-18', 'active', 'morning', 6000000, ARRAY['Medical Technology', 'Laboratory Safety'], ARRAY['Lab Testing', 'Equipment Maintenance']);
