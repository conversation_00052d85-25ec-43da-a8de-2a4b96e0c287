
-- Create enum for shift types
CREATE TYPE shift_status AS ENUM ('scheduled', 'completed', 'cancelled', 'no_show');

-- Create schedules table for work scheduling
CREATE TABLE public.schedules (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
  shift_date DATE NOT NULL,
  shift_type shift_type NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  status shift_status NOT NULL DEFAULT 'scheduled',
  notes TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(employee_id, shift_date, start_time)
);

-- Create leave requests table
CREATE TABLE public.leave_requests (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
  leave_type TEXT NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  reason TEXT,
  status TEXT NOT NULL DEFAULT 'pending',
  approved_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.leave_requests ENABLE ROW LEVEL SECURITY;

-- RLS Policies for schedules
CREATE POLICY "Authenticated users can view schedules"
  ON public.schedules
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can create schedules"
  ON public.schedules
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Authenticated users can update schedules"
  ON public.schedules
  FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can delete schedules"
  ON public.schedules
  FOR DELETE
  TO authenticated
  USING (true);

-- RLS Policies for leave requests
CREATE POLICY "Authenticated users can view leave requests"
  ON public.leave_requests
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can create leave requests"
  ON public.leave_requests
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update leave requests"
  ON public.leave_requests
  FOR UPDATE
  TO authenticated
  USING (true);

-- Insert sample schedule data for the first 3 employees
WITH employee_sample AS (
  SELECT id, ROW_NUMBER() OVER (ORDER BY created_at) as rn
  FROM employees
  LIMIT 3
),
date_series AS (
  SELECT generate_series(0, 6) as day_offset
)
INSERT INTO public.schedules (
  employee_id, shift_date, shift_type, start_time, end_time, status, notes
) 
SELECT 
  es.id,
  CURRENT_DATE + (ds.day_offset * interval '1 day'),
  CASE 
    WHEN ds.day_offset % 3 = 0 THEN 'morning'::shift_type
    WHEN ds.day_offset % 3 = 1 THEN 'afternoon'::shift_type
    ELSE 'night'::shift_type
  END,
  CASE 
    WHEN ds.day_offset % 3 = 0 THEN '07:00'::time
    WHEN ds.day_offset % 3 = 1 THEN '15:00'::time
    ELSE '23:00'::time
  END,
  CASE 
    WHEN ds.day_offset % 3 = 0 THEN '15:00'::time
    WHEN ds.day_offset % 3 = 1 THEN '23:00'::time
    ELSE '07:00'::time
  END,
  'scheduled'::shift_status,
  'Sample schedule entry'
FROM employee_sample es
CROSS JOIN date_series ds;
