// User activity logging service
import { Request } from 'express';
import { query } from '@/config/database';

export interface ActivityLogData {
  userId?: string;
  action: string;
  resource?: string;
  resourceId?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: any;
}

export class ActivityLogService {
  // Log user activity
  static async logActivity(data: ActivityLogData): Promise<void> {
    try {
      await query(
        `INSERT INTO user_activity_logs (user_id, action, resource, resource_id, ip_address, user_agent, metadata, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())`,
        [
          data.userId || null,
          data.action,
          data.resource || null,
          data.resourceId || null,
          data.ipAddress || null,
          data.userAgent || null,
          data.metadata ? JSON.stringify(data.metadata) : null
        ]
      );
    } catch (error) {
      console.error('Activity log error:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  // Log activity from Express request
  static async logFromRequest(
    req: Request,
    action: string,
    resource?: string,
    resourceId?: string,
    metadata?: any
  ): Promise<void> {
    const userId = (req as any).user?.id;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');

    await this.logActivity({
      userId,
      action,
      resource,
      resourceId,
      ipAddress,
      userAgent,
      metadata
    });
  }

  // Get user activity logs
  static async getUserActivityLogs(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<any[]> {
    try {
      const result = await query(
        `SELECT action, resource, resource_id, ip_address, user_agent, metadata, created_at
         FROM user_activity_logs
         WHERE user_id = $1
         ORDER BY created_at DESC
         LIMIT $2 OFFSET $3`,
        [userId, limit, offset]
      );

      return result.rows;
    } catch (error) {
      console.error('Get user activity logs error:', error);
      return [];
    }
  }

  // Get all activity logs (admin only)
  static async getAllActivityLogs(
    limit: number = 100,
    offset: number = 0,
    filters?: {
      userId?: string;
      action?: string;
      resource?: string;
      startDate?: Date;
      endDate?: Date;
    }
  ): Promise<any[]> {
    try {
      let whereClause = '';
      const params: any[] = [];
      let paramIndex = 1;

      if (filters) {
        const conditions: string[] = [];

        if (filters.userId) {
          conditions.push(`user_id = $${paramIndex++}`);
          params.push(filters.userId);
        }

        if (filters.action) {
          conditions.push(`action = $${paramIndex++}`);
          params.push(filters.action);
        }

        if (filters.resource) {
          conditions.push(`resource = $${paramIndex++}`);
          params.push(filters.resource);
        }

        if (filters.startDate) {
          conditions.push(`created_at >= $${paramIndex++}`);
          params.push(filters.startDate);
        }

        if (filters.endDate) {
          conditions.push(`created_at <= $${paramIndex++}`);
          params.push(filters.endDate);
        }

        if (conditions.length > 0) {
          whereClause = 'WHERE ' + conditions.join(' AND ');
        }
      }

      params.push(limit, offset);

      const result = await query(
        `SELECT ual.*, u.email as user_email, p.first_name, p.last_name
         FROM user_activity_logs ual
         LEFT JOIN users u ON ual.user_id = u.id
         LEFT JOIN profiles p ON ual.user_id = p.id
         ${whereClause}
         ORDER BY ual.created_at DESC
         LIMIT $${paramIndex++} OFFSET $${paramIndex++}`,
        params
      );

      return result.rows;
    } catch (error) {
      console.error('Get all activity logs error:', error);
      return [];
    }
  }

  // Clean up old activity logs (older than specified days)
  static async cleanupOldLogs(daysToKeep: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await query(
        'DELETE FROM user_activity_logs WHERE created_at < $1',
        [cutoffDate]
      );

      return result.rowCount || 0;
    } catch (error) {
      console.error('Cleanup old logs error:', error);
      return 0;
    }
  }
}

// Common activity actions
export const ActivityActions = {
  // Authentication
  LOGIN: 'login',
  LOGOUT: 'logout',
  REGISTER: 'register',
  PASSWORD_CHANGE: 'password_change',
  PASSWORD_RESET: 'password_reset',
  EMAIL_VERIFY: 'email_verify',
  
  // Profile
  PROFILE_UPDATE: 'profile_update',
  PROFILE_VIEW: 'profile_view',
  
  // Employee management
  EMPLOYEE_CREATE: 'employee_create',
  EMPLOYEE_UPDATE: 'employee_update',
  EMPLOYEE_DELETE: 'employee_delete',
  EMPLOYEE_VIEW: 'employee_view',
  
  // Schedule management
  SCHEDULE_CREATE: 'schedule_create',
  SCHEDULE_UPDATE: 'schedule_update',
  SCHEDULE_DELETE: 'schedule_delete',
  SCHEDULE_VIEW: 'schedule_view',
  
  // Leave requests
  LEAVE_REQUEST_CREATE: 'leave_request_create',
  LEAVE_REQUEST_UPDATE: 'leave_request_update',
  LEAVE_REQUEST_APPROVE: 'leave_request_approve',
  LEAVE_REQUEST_REJECT: 'leave_request_reject',
  
  // System
  SYSTEM_ACCESS: 'system_access',
  UNAUTHORIZED_ACCESS: 'unauthorized_access',
  API_ACCESS: 'api_access'
} as const;
