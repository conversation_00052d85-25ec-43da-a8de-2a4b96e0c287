
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Calendar, Settings } from 'lucide-react';
import { ScheduleForm } from './ScheduleForm';
import { AdvancedScheduleForm } from './AdvancedScheduleForm';
import { LeaveRequestForm } from './LeaveRequestForm';

interface ScheduleHeaderProps {
  isScheduleDialogOpen: boolean;
  setIsScheduleDialogOpen: (open: boolean) => void;
  isLeaveDialogOpen: boolean;
  setIsLeaveDialogOpen: (open: boolean) => void;
}

export function ScheduleHeader({
  isScheduleDialogOpen,
  setIsScheduleDialogOpen,
  isLeaveDialogOpen,
  setIsLeaveDialogOpen,
}: ScheduleHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Manajemen Jadwal</h1>
        <p className="text-gray-600">Kelola jadwal kerja dan cuti karyawan</p>
      </div>
      <div className="flex space-x-2">
        <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Tambah Jadwal
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Tambah Jadwal Kerja</DialogTitle>
            </DialogHeader>
            <Tabs defaultValue="simple" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="simple" className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  Jadwal Sederhana
                </TabsTrigger>
                <TabsTrigger value="advanced" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Jadwal Komprehensif
                </TabsTrigger>
              </TabsList>
              <TabsContent value="simple">
                <ScheduleForm onSuccess={() => setIsScheduleDialogOpen(false)} />
              </TabsContent>
              <TabsContent value="advanced">
                <AdvancedScheduleForm onSuccess={() => setIsScheduleDialogOpen(false)} />
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
        
        <Dialog open={isLeaveDialogOpen} onOpenChange={setIsLeaveDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline">
              <Calendar className="w-4 h-4 mr-2" />
              Ajukan Cuti
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Ajukan Permohonan Cuti</DialogTitle>
            </DialogHeader>
            <LeaveRequestForm onSuccess={() => setIsLeaveDialogOpen(false)} />
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
