
-- Drop all existing tables and recreate from scratch
-- WARNING: This will delete all existing data

-- Drop all tables in correct order (respecting foreign key dependencies)
DROP TABLE IF EXISTS public.schedule_replacements CASCADE;
DROP TABLE IF EXISTS public.medical_certificates CASCADE;
DROP TABLE IF EXISTS public.leave_approvals CASCADE;
DROP TABLE IF EXISTS public.leave_balances CASCADE;
DROP TABLE IF EXISTS public.leave_requests CASCADE;
DROP TABLE IF EXISTS public.schedules CASCADE;
DROP TABLE IF EXISTS public.notifications CASCADE;
DROP TABLE IF EXISTS public.positions CASCADE;
DROP TABLE IF EXISTS public.departments CASCADE;
DROP TABLE IF EXISTS public.roles CASCADE;
DROP TABLE IF EXISTS public.employees CASCADE;
DROP TABLE IF EXISTS public.profiles CASCADE;

-- Drop all triggers
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS on_profile_created ON public.profiles;

-- Drop all functions
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS public.sync_employee_profile() CASCADE;

-- Drop all enums
DROP TYPE IF EXISTS employee_role CASCADE;
DROP TYPE IF EXISTS department_type CASCADE;
DROP TYPE IF EXISTS employee_status CASCADE;
DROP TYPE IF EXISTS shift_type CASCADE;
DROP TYPE IF EXISTS shift_status CASCADE;

-- Recreate enums
CREATE TYPE employee_role AS ENUM ('admin', 'doctor', 'nurse', 'pharmacist', 'technician', 'receptionist', 'manager');
CREATE TYPE department_type AS ENUM ('emergency', 'surgery', 'pediatrics', 'cardiology', 'orthopedics', 'pharmacy', 'laboratory', 'radiology', 'administration', 'maintenance');
CREATE TYPE employee_status AS ENUM ('active', 'inactive', 'on_leave', 'terminated');
CREATE TYPE shift_type AS ENUM ('morning', 'afternoon', 'night', 'rotating', 'regular');
CREATE TYPE shift_status AS ENUM ('scheduled', 'completed', 'cancelled', 'no_show');

-- Create profiles table
CREATE TABLE public.profiles (
  id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  role employee_role DEFAULT 'nurse',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (id)
);

-- Create employees table
CREATE TABLE public.employees (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id TEXT NOT NULL UNIQUE,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT,
  role employee_role NOT NULL,
  department department_type NOT NULL,
  position TEXT NOT NULL,
  join_date DATE NOT NULL,
  status employee_status NOT NULL DEFAULT 'active',
  shift shift_type NOT NULL,
  salary DECIMAL(12,2),
  avatar TEXT,
  address TEXT,
  emergency_contact JSONB,
  certifications TEXT[],
  skills TEXT[],
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create schedules table
CREATE TABLE public.schedules (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
  shift_date DATE NOT NULL,
  shift_type shift_type NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  status shift_status NOT NULL DEFAULT 'scheduled',
  notes TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(employee_id, shift_date, start_time)
);

-- Create leave requests table
CREATE TABLE public.leave_requests (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
  leave_type TEXT NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  reason TEXT,
  status TEXT NOT NULL DEFAULT 'pending',
  approved_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.leave_requests ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for profiles
CREATE POLICY "Users can view their own profile"
  ON public.profiles
  FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- Create RLS policies for employees
CREATE POLICY "Authenticated users can view employees"
  ON public.employees
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can create employees"
  ON public.employees
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Authenticated users can update employees"
  ON public.employees
  FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can delete employees"
  ON public.employees
  FOR DELETE
  TO authenticated
  USING (true);

-- Create RLS policies for schedules
CREATE POLICY "Authenticated users can view schedules"
  ON public.schedules
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can create schedules"
  ON public.schedules
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Authenticated users can update schedules"
  ON public.schedules
  FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can delete schedules"
  ON public.schedules
  FOR DELETE
  TO authenticated
  USING (true);

-- Create RLS policies for leave requests
CREATE POLICY "Authenticated users can view leave requests"
  ON public.leave_requests
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can create leave requests"
  ON public.leave_requests
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update leave requests"
  ON public.leave_requests
  FOR UPDATE
  TO authenticated
  USING (true);

-- Create handle_new_user function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, email, role)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data ->> 'first_name',
    NEW.raw_user_meta_data ->> 'last_name',
    NEW.email,
    COALESCE(
      (NEW.raw_user_meta_data ->> 'role')::employee_role,
      'nurse'::employee_role
    )
  );
  RETURN NEW;
END;
$$;

-- Create sync_employee_profile function
CREATE OR REPLACE FUNCTION public.sync_employee_profile()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  UPDATE public.employees 
  SET user_id = NEW.id
  WHERE email = NEW.email AND user_id IS NULL;
  
  RETURN NEW;
END;
$$;

-- Create triggers
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

CREATE TRIGGER on_profile_created
  AFTER INSERT ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.sync_employee_profile();

-- Insert sample employees data
INSERT INTO public.employees (
  employee_id, first_name, last_name, email, phone, role, department, 
  position, join_date, status, shift, salary, certifications, skills
) VALUES
  ('DOC001', 'Dr. Sarah', 'Johnson', '<EMAIL>', '+62 812-3456-7890', 'doctor', 'cardiology', 'Cardiologist', '2020-01-15', 'active', 'morning', 25000000, ARRAY['Cardiology Specialist', 'Emergency Medicine'], ARRAY['ECG Interpretation', 'Cardiac Catheterization']),
  ('NUR001', 'Maria', 'Santos', '<EMAIL>', '+62 813-4567-8901', 'nurse', 'emergency', 'Head Nurse', '2019-03-20', 'active', 'night', 8000000, ARRAY['BLS', 'ACLS'], ARRAY['Patient Care', 'Emergency Response']),
  ('PHA001', 'Ahmad', 'Rizki', '<EMAIL>', '+62 814-5678-9012', 'pharmacist', 'pharmacy', 'Senior Pharmacist', '2021-06-10', 'active', 'morning', 12000000, ARRAY['Pharmacy License', 'Clinical Pharmacy'], ARRAY['Drug Dispensing', 'Patient Counseling']);
