// Permission management routes
import { Router, Response } from 'express';
import { PermissionService, PERMISSIONS } from '@/services/permissionService';
import { ManagementService } from '@/services/managementService';
import { ActivityLogService } from '@/services/activityLogService';
import { authenticateToken, AuthRequest } from '@/middleware/auth';
import { requirePermission, requireAnyPermission } from '@/middleware/permissions';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all available permissions (Admin/Manager only)
router.get('/available', requireAnyPermission([PERMISSIONS.ROLE_READ, PERMISSIONS.SYSTEM_ADMIN]), async (req: AuthRequest, res: Response) => {
  try {
    const permissions = PermissionService.getAllPermissions();
    const permissionGroups = PermissionService.getPermissionGroups();

    await ActivityLogService.logFromRequest(req, 'permissions_list_view', 'permissions');

    res.json({
      permissions,
      permissionGroups,
      message: 'Available permissions retrieved successfully'
    });
  } catch (error) {
    console.error('Get available permissions error:', error);
    res.status(500).json({ error: 'Failed to fetch available permissions' });
  }
});

// Get user's permissions
router.get('/user/:userId', requireAnyPermission([PERMISSIONS.USER_READ, PERMISSIONS.SYSTEM_ADMIN]), async (req: AuthRequest, res: Response) => {
  try {
    const { userId } = req.params;

    // Users can only view their own permissions unless they have admin/user read permissions
    if (userId !== req.user!.id) {
      const hasPermission = await PermissionService.userHasAnyPermission(req.user!.id, [PERMISSIONS.USER_READ, PERMISSIONS.SYSTEM_ADMIN]);
      if (!hasPermission) {
        return res.status(403).json({ error: 'Cannot view other users permissions' });
      }
    }

    const permissions = await PermissionService.getUserPermissions(userId);

    await ActivityLogService.logFromRequest(req, 'user_permissions_view', 'user', userId);

    res.json({
      userId,
      permissions,
      message: 'User permissions retrieved successfully'
    });
  } catch (error) {
    console.error('Get user permissions error:', error);
    res.status(500).json({ error: 'Failed to fetch user permissions' });
  }
});

// Get current user's permissions
router.get('/me', async (req: AuthRequest, res: Response) => {
  try {
    const permissions = await PermissionService.getUserPermissions(req.user!.id);

    res.json({
      userId: req.user!.id,
      permissions,
      message: 'Your permissions retrieved successfully'
    });
  } catch (error) {
    console.error('Get current user permissions error:', error);
    res.status(500).json({ error: 'Failed to fetch your permissions' });
  }
});

// Check if user has specific permission
router.post('/check', async (req: AuthRequest, res: Response) => {
  try {
    const { permission, userId } = req.body;

    if (!permission) {
      return res.status(400).json({ error: 'Permission is required' });
    }

    const targetUserId = userId || req.user!.id;

    // Users can only check their own permissions unless they have admin permissions
    if (targetUserId !== req.user!.id) {
      const hasPermission = await PermissionService.userHasAnyPermission(req.user!.id, [PERMISSIONS.USER_READ, PERMISSIONS.SYSTEM_ADMIN]);
      if (!hasPermission) {
        return res.status(403).json({ error: 'Cannot check other users permissions' });
      }
    }

    const hasPermission = await PermissionService.userHasPermission(targetUserId, permission);

    res.json({
      userId: targetUserId,
      permission,
      hasPermission,
      message: hasPermission ? 'User has permission' : 'User does not have permission'
    });
  } catch (error) {
    console.error('Check permission error:', error);
    res.status(500).json({ error: 'Failed to check permission' });
  }
});

// Check if user has any of the specified permissions
router.post('/check-any', async (req: AuthRequest, res: Response) => {
  try {
    const { permissions, userId } = req.body;

    if (!Array.isArray(permissions) || permissions.length === 0) {
      return res.status(400).json({ error: 'Permissions array is required' });
    }

    const targetUserId = userId || req.user!.id;

    // Users can only check their own permissions unless they have admin permissions
    if (targetUserId !== req.user!.id) {
      const hasPermission = await PermissionService.userHasAnyPermission(req.user!.id, [PERMISSIONS.USER_READ, PERMISSIONS.SYSTEM_ADMIN]);
      if (!hasPermission) {
        return res.status(403).json({ error: 'Cannot check other users permissions' });
      }
    }

    const hasAnyPermission = await PermissionService.userHasAnyPermission(targetUserId, permissions);

    res.json({
      userId: targetUserId,
      permissions,
      hasAnyPermission,
      message: hasAnyPermission ? 'User has at least one permission' : 'User does not have any of the specified permissions'
    });
  } catch (error) {
    console.error('Check any permission error:', error);
    res.status(500).json({ error: 'Failed to check permissions' });
  }
});

// Update role permissions (Admin only)
router.put('/role/:roleId', requirePermission(PERMISSIONS.ROLE_UPDATE), async (req: AuthRequest, res: Response) => {
  try {
    const { roleId } = req.params;
    const { permissions } = req.body;

    if (!Array.isArray(permissions)) {
      return res.status(400).json({ error: 'Permissions must be an array' });
    }

    // Validate permissions
    const validation = PermissionService.validatePermissions(permissions);
    if (validation.invalid.length > 0) {
      return res.status(400).json({ 
        error: 'Invalid permissions found',
        invalidPermissions: validation.invalid
      });
    }

    // Check if role exists
    const role = await ManagementService.getRoleById(roleId);
    if (!role) {
      return res.status(404).json({ error: 'Role not found' });
    }

    await PermissionService.updateRolePermissions(roleId, validation.valid);

    await ActivityLogService.logFromRequest(req, 'role_permissions_update', 'role', roleId, {
      permissions: validation.valid
    });

    res.json({
      message: 'Role permissions updated successfully',
      roleId,
      permissions: validation.valid
    });
  } catch (error) {
    console.error('Update role permissions error:', error);
    res.status(500).json({ error: 'Failed to update role permissions' });
  }
});

// Get role permissions
router.get('/role/:roleId', requireAnyPermission([PERMISSIONS.ROLE_READ, PERMISSIONS.SYSTEM_ADMIN]), async (req: AuthRequest, res: Response) => {
  try {
    const { roleId } = req.params;

    const role = await ManagementService.getRoleById(roleId);
    if (!role) {
      return res.status(404).json({ error: 'Role not found' });
    }

    await ActivityLogService.logFromRequest(req, 'role_permissions_view', 'role', roleId);

    res.json({
      roleId,
      roleName: role.name,
      permissions: role.permissions || [],
      message: 'Role permissions retrieved successfully'
    });
  } catch (error) {
    console.error('Get role permissions error:', error);
    res.status(500).json({ error: 'Failed to fetch role permissions' });
  }
});

// Get default permissions for a role type
router.get('/defaults/:roleType', requireAnyPermission([PERMISSIONS.ROLE_READ, PERMISSIONS.SYSTEM_ADMIN]), async (req: AuthRequest, res: Response) => {
  try {
    const { roleType } = req.params;

    const defaultPermissions = PermissionService.getDefaultPermissions(roleType);

    res.json({
      roleType,
      defaultPermissions,
      message: 'Default permissions retrieved successfully'
    });
  } catch (error) {
    console.error('Get default permissions error:', error);
    res.status(500).json({ error: 'Failed to fetch default permissions' });
  }
});

// Validate permissions
router.post('/validate', requireAnyPermission([PERMISSIONS.ROLE_UPDATE, PERMISSIONS.SYSTEM_ADMIN]), async (req: AuthRequest, res: Response) => {
  try {
    const { permissions } = req.body;

    if (!Array.isArray(permissions)) {
      return res.status(400).json({ error: 'Permissions must be an array' });
    }

    const validation = PermissionService.validatePermissions(permissions);

    res.json({
      valid: validation.valid,
      invalid: validation.invalid,
      isValid: validation.invalid.length === 0,
      message: validation.invalid.length === 0 ? 'All permissions are valid' : 'Some permissions are invalid'
    });
  } catch (error) {
    console.error('Validate permissions error:', error);
    res.status(500).json({ error: 'Failed to validate permissions' });
  }
});

export default router;
