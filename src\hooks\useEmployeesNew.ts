// Employees hook using API endpoints
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { employeeApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Employee } from '@/types/employee';

// Transform API response to frontend format
const transformEmployeeFromApi = (apiEmployee: any): Employee => ({
  id: apiEmployee.id,
  employeeId: apiEmployee.employee_id,
  firstName: apiEmployee.first_name,
  lastName: apiEmployee.last_name,
  email: apiEmployee.email,
  phone: apiEmployee.phone || '',
  role: apiEmployee.role,
  department: apiEmployee.department,
  position: apiEmployee.position,
  joinDate: apiEmployee.join_date,
  status: apiEmployee.status,
  shift: apiEmployee.shift,
  salary: apiEmployee.salary,
  avatar: apiEmployee.avatar,
  address: apiEmployee.address,
  userId: apiEmployee.user_id,
  certifications: [], // Default empty array
  skills: [], // Default empty array
});

// Transform frontend data to API format
const transformEmployeeToApi = (employee: Partial<Employee>) => ({
  employeeId: employee.employeeId,
  firstName: employee.firstName,
  lastName: employee.lastName,
  email: employee.email,
  phone: employee.phone,
  role: employee.role,
  department: employee.department,
  position: employee.position,
  joinDate: employee.joinDate,
  status: employee.status,
  shift: employee.shift,
  salary: employee.salary,
  avatar: employee.avatar,
  address: employee.address,
  userId: employee.userId,
});

export function useEmployees(params?: {
  department?: string;
  role?: string;
  search?: string;
}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all employees
  const { data: employeesData, isLoading, error } = useQuery({
    queryKey: ['employees', params],
    queryFn: () => employeeApi.getAll(params),
  });

  const employees = (employeesData?.employees || []).map(transformEmployeeFromApi);

  // Create employee mutation
  const createEmployeeMutation = useMutation({
    mutationFn: async (employeeData: Partial<Employee>) => {
      const apiData = transformEmployeeToApi(employeeData);
      const response = await employeeApi.create(apiData);
      return transformEmployeeFromApi(response.employee);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({
        title: "Berhasil",
        description: "Karyawan berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menambahkan karyawan",
        variant: "destructive",
      });
    },
  });

  // Update employee mutation
  const updateEmployeeMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Employee> }) => {
      const apiData = transformEmployeeToApi(data);
      const response = await employeeApi.update(id, apiData);
      return transformEmployeeFromApi(response.employee);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({
        title: "Berhasil",
        description: "Data karyawan berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui data karyawan",
        variant: "destructive",
      });
    },
  });

  // Delete employee mutation
  const deleteEmployeeMutation = useMutation({
    mutationFn: async (id: string) => {
      await employeeApi.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({
        title: "Berhasil",
        description: "Karyawan berhasil dihapus",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menghapus karyawan",
        variant: "destructive",
      });
    },
  });

  return {
    employees,
    isLoading,
    error,
    createEmployee: createEmployeeMutation.mutate,
    updateEmployee: updateEmployeeMutation.mutate,
    deleteEmployee: deleteEmployeeMutation.mutate,
    isCreating: createEmployeeMutation.isPending,
    isUpdating: updateEmployeeMutation.isPending,
    isDeleting: deleteEmployeeMutation.isPending,
  };
}

// Hook for getting a single employee
export function useEmployee(id: string) {
  return useQuery({
    queryKey: ['employee', id],
    queryFn: async () => {
      const response = await employeeApi.getById(id);
      return transformEmployeeFromApi(response.employee);
    },
    enabled: !!id,
  });
}

// Hook for employee statistics
export function useEmployeeStatistics() {
  return useQuery({
    queryKey: ['employee-statistics'],
    queryFn: async () => {
      return await employeeApi.getStatistics();
    },
  });
}
