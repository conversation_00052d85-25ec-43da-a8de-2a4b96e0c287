// Admin routes for user management
import { Router, Response } from 'express';
import { AuthService } from '@/services/authService';
import { ActivityLogService } from '@/services/activityLogService';
import { authenticateToken, requireRole, AuthRequest } from '@/middleware/auth';
import { validate, authSchemas } from '@/middleware/validation';
import { query } from '@/config/database';

const router = Router();

// Apply admin authentication to all routes
router.use(authenticateToken);
router.use(requireRole(['admin']));

// Get all users (admin only)
router.get('/users', async (req: AuthRequest, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const search = req.query.search as string;
    const offset = (page - 1) * limit;

    let whereClause = '';
    const params: any[] = [];
    let paramIndex = 1;

    if (search) {
      whereClause = `WHERE u.email ILIKE $${paramIndex} OR p.first_name ILIKE $${paramIndex} OR p.last_name ILIKE $${paramIndex}`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as total
       FROM users u
       LEFT JOIN profiles p ON u.id = p.id
       ${whereClause}`,
      params
    );

    const total = parseInt(countResult.rows[0].total);

    // Get users
    params.push(limit, offset);
    const result = await query(
      `SELECT u.id, u.email, u.email_confirmed, u.created_at, u.updated_at,
              p.first_name, p.last_name, p.role
       FROM users u
       LEFT JOIN profiles p ON u.id = p.id
       ${whereClause}
       ORDER BY u.created_at DESC
       LIMIT $${paramIndex++} OFFSET $${paramIndex++}`,
      params
    );

    await ActivityLogService.logFromRequest(req, 'admin_users_list', 'users');

    res.json({
      users: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get users endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user by ID (admin only)
router.get('/users/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    const user = await AuthService.getUserById(id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    await ActivityLogService.logFromRequest(req, 'admin_user_view', 'user', id);

    res.json({ user });
  } catch (error) {
    console.error('Get user by ID endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user (admin only)
router.put('/users/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { firstName, lastName, email, role, emailConfirmed } = req.body;

    // Check if user exists
    const existingUser = await AuthService.getUserById(id);
    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Update user email_confirmed if provided
    if (emailConfirmed !== undefined) {
      await query(
        'UPDATE users SET email_confirmed = $1, updated_at = NOW() WHERE id = $2',
        [emailConfirmed, id]
      );
    }

    // Update profile
    const profileData: any = {};
    if (firstName !== undefined) profileData.first_name = firstName;
    if (lastName !== undefined) profileData.last_name = lastName;
    if (email !== undefined) profileData.email = email.toLowerCase();
    if (role !== undefined) profileData.role = role;

    if (Object.keys(profileData).length > 0) {
      const result = await AuthService.updateProfile(id, profileData);
      if (result.error) {
        return res.status(400).json({ error: result.error });
      }
    }

    await ActivityLogService.logFromRequest(req, 'admin_user_update', 'user', id, {
      updatedFields: Object.keys({ ...profileData, ...(emailConfirmed !== undefined && { emailConfirmed }) })
    });

    res.json({ message: 'User updated successfully' });
  } catch (error) {
    console.error('Update user endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Suspend/Activate user (admin only)
router.put('/users/:id/status', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { suspended } = req.body;

    if (suspended === undefined) {
      return res.status(400).json({ error: 'Suspended status is required' });
    }

    // Check if user exists
    const existingUser = await AuthService.getUserById(id);
    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Prevent admin from suspending themselves
    if (id === req.user!.id) {
      return res.status(400).json({ error: 'Cannot change your own status' });
    }

    // Add suspended field to users table (you might need to add this column)
    await query(
      'UPDATE users SET suspended = $1, updated_at = NOW() WHERE id = $2',
      [suspended, id]
    );

    await ActivityLogService.logFromRequest(req, suspended ? 'admin_user_suspend' : 'admin_user_activate', 'user', id);

    res.json({ 
      message: suspended ? 'User suspended successfully' : 'User activated successfully' 
    });
  } catch (error) {
    console.error('Update user status endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete user (admin only)
router.delete('/users/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    // Check if user exists
    const existingUser = await AuthService.getUserById(id);
    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Prevent admin from deleting themselves
    if (id === req.user!.id) {
      return res.status(400).json({ error: 'Cannot delete your own account' });
    }

    // Delete user (cascade will handle profile deletion)
    await query('DELETE FROM users WHERE id = $1', [id]);

    await ActivityLogService.logFromRequest(req, 'admin_user_delete', 'user', id);

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Delete user endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user activity logs (admin only)
router.get('/users/:id/activity', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const logs = await ActivityLogService.getUserActivityLogs(id, limit, offset);

    await ActivityLogService.logFromRequest(req, 'admin_user_activity_view', 'user', id);

    res.json({ logs });
  } catch (error) {
    console.error('Get user activity endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all activity logs (admin only)
router.get('/activity-logs', async (req: AuthRequest, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 100;
    const offset = parseInt(req.query.offset as string) || 0;
    
    const filters: any = {};
    if (req.query.userId) filters.userId = req.query.userId as string;
    if (req.query.action) filters.action = req.query.action as string;
    if (req.query.resource) filters.resource = req.query.resource as string;
    if (req.query.startDate) filters.startDate = new Date(req.query.startDate as string);
    if (req.query.endDate) filters.endDate = new Date(req.query.endDate as string);

    const logs = await ActivityLogService.getAllActivityLogs(limit, offset, filters);

    await ActivityLogService.logFromRequest(req, 'admin_activity_logs_view', 'activity_logs');

    res.json({ logs });
  } catch (error) {
    console.error('Get activity logs endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// System statistics (admin only)
router.get('/stats', async (req: AuthRequest, res: Response) => {
  try {
    const stats = await Promise.all([
      query('SELECT COUNT(*) as total_users FROM users'),
      query('SELECT COUNT(*) as active_users FROM users WHERE email_confirmed = true AND (suspended IS NULL OR suspended = false)'),
      query('SELECT COUNT(*) as pending_verification FROM users WHERE email_confirmed = false'),
      query('SELECT COUNT(*) as suspended_users FROM users WHERE suspended = true'),
      query(`SELECT COUNT(*) as recent_logins FROM user_activity_logs 
             WHERE action = 'login' AND created_at > NOW() - INTERVAL '24 hours'`),
      query(`SELECT COUNT(*) as total_employees FROM employees`),
    ]);

    const statistics = {
      totalUsers: parseInt(stats[0].rows[0].total_users),
      activeUsers: parseInt(stats[1].rows[0].active_users),
      pendingVerification: parseInt(stats[2].rows[0].pending_verification),
      suspendedUsers: parseInt(stats[3].rows[0].suspended_users),
      recentLogins: parseInt(stats[4].rows[0].recent_logins),
      totalEmployees: parseInt(stats[5].rows[0].total_employees),
    };

    await ActivityLogService.logFromRequest(req, 'admin_stats_view', 'system');

    res.json({ statistics });
  } catch (error) {
    console.error('Get stats endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
