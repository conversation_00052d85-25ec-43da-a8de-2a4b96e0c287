// Admin routes for user management
import { Router, Response } from 'express';
import { AuthService } from '@/services/authService';
import { ManagementService } from '@/services/managementService';
import { ActivityLogService } from '@/services/activityLogService';
import { authenticateToken, requireRole, AuthRequest } from '@/middleware/auth';
import { validate, authSchemas } from '@/middleware/validation';
import { query } from '@/config/database';

const router = Router();

// Apply admin authentication to all routes
router.use(authenticateToken);
router.use(requireRole(['admin']));

// Get all users (admin only)
router.get('/users', async (req: AuthRequest, res: Response) => {
  try {
    const options = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      search: req.query.search as string,
      role: req.query.role as string,
      status: req.query.status as string,
      sortBy: req.query.sortBy as string,
      sortOrder: (req.query.sortOrder as 'ASC' | 'DESC') || 'DESC'
    };

    const result = await ManagementService.getAllUsers(options);

    await ActivityLogService.logFromRequest(req, 'admin_users_list', 'users');

    res.json(result);
  } catch (error) {
    console.error('Get users endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user by ID (admin only)
router.get('/users/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    const user = await AuthService.getUserById(id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    await ActivityLogService.logFromRequest(req, 'admin_user_view', 'user', id);

    res.json({ user });
  } catch (error) {
    console.error('Get user by ID endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user (admin only)
router.put('/users/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { firstName, lastName, email, role, emailConfirmed } = req.body;

    // Check if user exists
    const existingUser = await AuthService.getUserById(id);
    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Update user email_confirmed if provided
    if (emailConfirmed !== undefined) {
      await query(
        'UPDATE users SET email_confirmed = $1, updated_at = NOW() WHERE id = $2',
        [emailConfirmed, id]
      );
    }

    // Update profile
    const profileData: any = {};
    if (firstName !== undefined) profileData.first_name = firstName;
    if (lastName !== undefined) profileData.last_name = lastName;
    if (email !== undefined) profileData.email = email.toLowerCase();
    if (role !== undefined) profileData.role = role;

    if (Object.keys(profileData).length > 0) {
      const result = await AuthService.updateProfile(id, profileData);
      if (result.error) {
        return res.status(400).json({ error: result.error });
      }
    }

    await ActivityLogService.logFromRequest(req, 'admin_user_update', 'user', id, {
      updatedFields: Object.keys({ ...profileData, ...(emailConfirmed !== undefined && { emailConfirmed }) })
    });

    res.json({ message: 'User updated successfully' });
  } catch (error) {
    console.error('Update user endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Suspend/Activate user (admin only)
router.put('/users/:id/status', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { suspended } = req.body;

    if (suspended === undefined) {
      return res.status(400).json({ error: 'Suspended status is required' });
    }

    // Check if user exists
    const existingUser = await AuthService.getUserById(id);
    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Prevent admin from suspending themselves
    if (id === req.user!.id) {
      return res.status(400).json({ error: 'Cannot change your own status' });
    }

    // Add suspended field to users table (you might need to add this column)
    await query(
      'UPDATE users SET suspended = $1, updated_at = NOW() WHERE id = $2',
      [suspended, id]
    );

    await ActivityLogService.logFromRequest(req, suspended ? 'admin_user_suspend' : 'admin_user_activate', 'user', id);

    res.json({ 
      message: suspended ? 'User suspended successfully' : 'User activated successfully' 
    });
  } catch (error) {
    console.error('Update user status endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete user (admin only)
router.delete('/users/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    // Check if user exists
    const existingUser = await AuthService.getUserById(id);
    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Prevent admin from deleting themselves
    if (id === req.user!.id) {
      return res.status(400).json({ error: 'Cannot delete your own account' });
    }

    // Delete user (cascade will handle profile deletion)
    await query('DELETE FROM users WHERE id = $1', [id]);

    await ActivityLogService.logFromRequest(req, 'admin_user_delete', 'user', id);

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Delete user endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user activity logs (admin only)
router.get('/users/:id/activity', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const logs = await ActivityLogService.getUserActivityLogs(id, limit, offset);

    await ActivityLogService.logFromRequest(req, 'admin_user_activity_view', 'user', id);

    res.json({ logs });
  } catch (error) {
    console.error('Get user activity endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all activity logs (admin only)
router.get('/activity-logs', async (req: AuthRequest, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 100;
    const offset = parseInt(req.query.offset as string) || 0;
    
    const filters: any = {};
    if (req.query.userId) filters.userId = req.query.userId as string;
    if (req.query.action) filters.action = req.query.action as string;
    if (req.query.resource) filters.resource = req.query.resource as string;
    if (req.query.startDate) filters.startDate = new Date(req.query.startDate as string);
    if (req.query.endDate) filters.endDate = new Date(req.query.endDate as string);

    const logs = await ActivityLogService.getAllActivityLogs(limit, offset, filters);

    await ActivityLogService.logFromRequest(req, 'admin_activity_logs_view', 'activity_logs');

    res.json({ logs });
  } catch (error) {
    console.error('Get activity logs endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create user (admin only)
router.post('/users', validate(authSchemas.register), async (req: AuthRequest, res: Response) => {
  try {
    const { email, password, firstName, lastName, role, emailConfirmed } = req.body;

    const result = await ManagementService.createUser({
      email,
      password,
      firstName,
      lastName,
      role,
      emailConfirmed
    });

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    await ActivityLogService.logFromRequest(req, 'admin_user_create', 'user', result.user.id);

    res.status(201).json({
      message: 'User created successfully',
      user: result.user
    });
  } catch (error) {
    console.error('Create user endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Bulk create users (admin only)
router.post('/users/bulk', async (req: AuthRequest, res: Response) => {
  try {
    const { users } = req.body;

    if (!Array.isArray(users) || users.length === 0) {
      return res.status(400).json({ error: 'Users array is required' });
    }

    const result = await ManagementService.bulkCreateUsers(users);

    await ActivityLogService.logFromRequest(req, 'admin_users_bulk_create', 'users', null, {
      created: result.created.length,
      errors: result.errors.length
    });

    res.json({
      message: `Bulk operation completed. Created: ${result.created.length}, Errors: ${result.errors.length}`,
      created: result.created,
      errors: result.errors
    });
  } catch (error) {
    console.error('Bulk create users endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Bulk update users (admin only)
router.put('/users/bulk', async (req: AuthRequest, res: Response) => {
  try {
    const { updates } = req.body;

    if (!Array.isArray(updates) || updates.length === 0) {
      return res.status(400).json({ error: 'Updates array is required' });
    }

    const result = await ManagementService.bulkUpdateUsers(updates);

    await ActivityLogService.logFromRequest(req, 'admin_users_bulk_update', 'users', null, {
      updated: result.updated.length,
      errors: result.errors.length
    });

    res.json({
      message: `Bulk operation completed. Updated: ${result.updated.length}, Errors: ${result.errors.length}`,
      updated: result.updated,
      errors: result.errors
    });
  } catch (error) {
    console.error('Bulk update users endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Bulk delete users (admin only)
router.delete('/users/bulk', async (req: AuthRequest, res: Response) => {
  try {
    const { userIds } = req.body;

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ error: 'User IDs array is required' });
    }

    // Prevent admin from deleting themselves
    if (userIds.includes(req.user!.id)) {
      return res.status(400).json({ error: 'Cannot delete your own account' });
    }

    const result = await ManagementService.bulkDeleteUsers(userIds);

    await ActivityLogService.logFromRequest(req, 'admin_users_bulk_delete', 'users', null, {
      deleted: result.deleted.length,
      errors: result.errors.length
    });

    res.json({
      message: `Bulk operation completed. Deleted: ${result.deleted.length}, Errors: ${result.errors.length}`,
      deleted: result.deleted,
      errors: result.errors
    });
  } catch (error) {
    console.error('Bulk delete users endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Reset user password (admin only)
router.post('/users/:id/reset-password', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;

    if (!newPassword || newPassword.length < 6) {
      return res.status(400).json({ error: 'New password must be at least 6 characters' });
    }

    await ManagementService.resetUserPassword(id, newPassword);

    await ActivityLogService.logFromRequest(req, 'admin_user_password_reset', 'user', id);

    res.json({ message: 'Password reset successfully' });
  } catch (error) {
    console.error('Reset password endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Unlock user account (admin only)
router.post('/users/:id/unlock', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    await ManagementService.unlockUser(id);

    await ActivityLogService.logFromRequest(req, 'admin_user_unlock', 'user', id);

    res.json({ message: 'User account unlocked successfully' });
  } catch (error) {
    console.error('Unlock user endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// System statistics (admin only)
router.get('/stats', async (req: AuthRequest, res: Response) => {
  try {
    const userStats = await ManagementService.getUserStats();

    // Get additional system stats
    const employeeCount = await query('SELECT COUNT(*) as total_employees FROM employees');

    const statistics = {
      ...userStats,
      totalEmployees: parseInt(employeeCount.rows[0].total_employees)
    };

    await ActivityLogService.logFromRequest(req, 'admin_stats_view', 'system');

    res.json({ statistics });
  } catch (error) {
    console.error('Get stats endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
