
import { useRoleAccess } from '@/hooks/useRoleAccess';
import { EmployeeRole } from '@/types/employee';

interface RoleBasedMenuItemProps {
  children: React.ReactNode;
  allowedRoles: EmployeeRole[];
}

export function RoleBasedMenuItem({ children, allowedRoles }: RoleBasedMenuItemProps) {
  const { hasAnyRole, isLoading } = useRoleAccess();

  if (isLoading || !hasAnyRole(allowedRoles)) {
    return null;
  }

  return <>{children}</>;
}
