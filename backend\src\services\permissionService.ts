// Permission management service
import { query, Role } from '@/config/database';

// Define available permissions
export const PERMISSIONS = {
  // User Management
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  USER_BULK_OPERATIONS: 'user:bulk',
  USER_PASSWORD_RESET: 'user:password_reset',
  USER_STATUS_CHANGE: 'user:status_change',

  // Employee Management
  EMPLOYEE_CREATE: 'employee:create',
  EMPLOYEE_READ: 'employee:read',
  <PERSON>MP<PERSON><PERSON>YEE_UPDATE: 'employee:update',
  EMPLOYEE_DELETE: 'employee:delete',
  <PERSON><PERSON><PERSON><PERSON>YEE_BULK_OPERATIONS: 'employee:bulk',

  // Department Management
  DEPARTMENT_CREATE: 'department:create',
  DEPARTMENT_READ: 'department:read',
  DEPARTMENT_UPDATE: 'department:update',
  DEPARTMENT_DELETE: 'department:delete',

  // Role Management
  ROLE_CREATE: 'role:create',
  ROLE_READ: 'role:read',
  R<PERSON><PERSON>_UPDATE: 'role:update',
  R<PERSON><PERSON>_DELETE: 'role:delete',

  // Position Management
  POSITION_CREATE: 'position:create',
  POSITION_READ: 'position:read',
  POSITION_UPDATE: 'position:update',
  POSITION_DELETE: 'position:delete',

  // Schedule Management
  SCHEDULE_CREATE: 'schedule:create',
  SCHEDULE_READ: 'schedule:read',
  SCHEDULE_UPDATE: 'schedule:update',
  SCHEDULE_DELETE: 'schedule:delete',
  SCHEDULE_ASSIGN: 'schedule:assign',

  // Leave Request Management
  LEAVE_REQUEST_CREATE: 'leave_request:create',
  LEAVE_REQUEST_READ: 'leave_request:read',
  LEAVE_REQUEST_UPDATE: 'leave_request:update',
  LEAVE_REQUEST_DELETE: 'leave_request:delete',
  LEAVE_REQUEST_APPROVE: 'leave_request:approve',

  // System Administration
  SYSTEM_ADMIN: 'system:admin',
  SYSTEM_STATS: 'system:stats',
  SYSTEM_LOGS: 'system:logs',
  SYSTEM_BACKUP: 'system:backup',

  // Reports
  REPORTS_VIEW: 'reports:view',
  REPORTS_EXPORT: 'reports:export',
  REPORTS_ANALYTICS: 'reports:analytics',
} as const;

// Permission groups for easier management
export const PERMISSION_GROUPS = {
  USER_MANAGEMENT: {
    name: 'User Management',
    description: 'Permissions for managing user accounts',
    permissions: [
      PERMISSIONS.USER_CREATE,
      PERMISSIONS.USER_READ,
      PERMISSIONS.USER_UPDATE,
      PERMISSIONS.USER_DELETE,
      PERMISSIONS.USER_BULK_OPERATIONS,
      PERMISSIONS.USER_PASSWORD_RESET,
      PERMISSIONS.USER_STATUS_CHANGE,
    ],
  },
  EMPLOYEE_MANAGEMENT: {
    name: 'Employee Management',
    description: 'Permissions for managing employee records',
    permissions: [
      PERMISSIONS.EMPLOYEE_CREATE,
      PERMISSIONS.EMPLOYEE_READ,
      PERMISSIONS.EMPLOYEE_UPDATE,
      PERMISSIONS.EMPLOYEE_DELETE,
      PERMISSIONS.EMPLOYEE_BULK_OPERATIONS,
    ],
  },
  ORGANIZATIONAL_MANAGEMENT: {
    name: 'Organizational Management',
    description: 'Permissions for managing departments, roles, and positions',
    permissions: [
      PERMISSIONS.DEPARTMENT_CREATE,
      PERMISSIONS.DEPARTMENT_READ,
      PERMISSIONS.DEPARTMENT_UPDATE,
      PERMISSIONS.DEPARTMENT_DELETE,
      PERMISSIONS.ROLE_CREATE,
      PERMISSIONS.ROLE_READ,
      PERMISSIONS.ROLE_UPDATE,
      PERMISSIONS.ROLE_DELETE,
      PERMISSIONS.POSITION_CREATE,
      PERMISSIONS.POSITION_READ,
      PERMISSIONS.POSITION_UPDATE,
      PERMISSIONS.POSITION_DELETE,
    ],
  },
  SCHEDULE_MANAGEMENT: {
    name: 'Schedule Management',
    description: 'Permissions for managing work schedules',
    permissions: [
      PERMISSIONS.SCHEDULE_CREATE,
      PERMISSIONS.SCHEDULE_READ,
      PERMISSIONS.SCHEDULE_UPDATE,
      PERMISSIONS.SCHEDULE_DELETE,
      PERMISSIONS.SCHEDULE_ASSIGN,
    ],
  },
  LEAVE_MANAGEMENT: {
    name: 'Leave Management',
    description: 'Permissions for managing leave requests',
    permissions: [
      PERMISSIONS.LEAVE_REQUEST_CREATE,
      PERMISSIONS.LEAVE_REQUEST_READ,
      PERMISSIONS.LEAVE_REQUEST_UPDATE,
      PERMISSIONS.LEAVE_REQUEST_DELETE,
      PERMISSIONS.LEAVE_REQUEST_APPROVE,
    ],
  },
  SYSTEM_ADMINISTRATION: {
    name: 'System Administration',
    description: 'System-level administrative permissions',
    permissions: [
      PERMISSIONS.SYSTEM_ADMIN,
      PERMISSIONS.SYSTEM_STATS,
      PERMISSIONS.SYSTEM_LOGS,
      PERMISSIONS.SYSTEM_BACKUP,
    ],
  },
  REPORTS: {
    name: 'Reports & Analytics',
    description: 'Permissions for viewing and generating reports',
    permissions: [
      PERMISSIONS.REPORTS_VIEW,
      PERMISSIONS.REPORTS_EXPORT,
      PERMISSIONS.REPORTS_ANALYTICS,
    ],
  },
};

// Default role permissions
export const DEFAULT_ROLE_PERMISSIONS = {
  admin: Object.values(PERMISSIONS),
  manager: [
    // User management (limited)
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_STATUS_CHANGE,
    
    // Employee management
    ...PERMISSION_GROUPS.EMPLOYEE_MANAGEMENT.permissions,
    
    // Organizational management (limited)
    PERMISSIONS.DEPARTMENT_READ,
    PERMISSIONS.POSITION_READ,
    PERMISSIONS.ROLE_READ,
    
    // Schedule management
    ...PERMISSION_GROUPS.SCHEDULE_MANAGEMENT.permissions,
    
    // Leave management
    ...PERMISSION_GROUPS.LEAVE_MANAGEMENT.permissions,
    
    // Reports
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.REPORTS_EXPORT,
  ],
  doctor: [
    PERMISSIONS.EMPLOYEE_READ,
    PERMISSIONS.SCHEDULE_READ,
    PERMISSIONS.LEAVE_REQUEST_CREATE,
    PERMISSIONS.LEAVE_REQUEST_READ,
    PERMISSIONS.LEAVE_REQUEST_UPDATE,
  ],
  nurse: [
    PERMISSIONS.EMPLOYEE_READ,
    PERMISSIONS.SCHEDULE_READ,
    PERMISSIONS.LEAVE_REQUEST_CREATE,
    PERMISSIONS.LEAVE_REQUEST_READ,
    PERMISSIONS.LEAVE_REQUEST_UPDATE,
  ],
  pharmacist: [
    PERMISSIONS.EMPLOYEE_READ,
    PERMISSIONS.SCHEDULE_READ,
    PERMISSIONS.LEAVE_REQUEST_CREATE,
    PERMISSIONS.LEAVE_REQUEST_READ,
    PERMISSIONS.LEAVE_REQUEST_UPDATE,
  ],
  technician: [
    PERMISSIONS.EMPLOYEE_READ,
    PERMISSIONS.SCHEDULE_READ,
    PERMISSIONS.LEAVE_REQUEST_CREATE,
    PERMISSIONS.LEAVE_REQUEST_READ,
    PERMISSIONS.LEAVE_REQUEST_UPDATE,
  ],
  receptionist: [
    PERMISSIONS.EMPLOYEE_READ,
    PERMISSIONS.SCHEDULE_READ,
    PERMISSIONS.LEAVE_REQUEST_CREATE,
    PERMISSIONS.LEAVE_REQUEST_READ,
    PERMISSIONS.LEAVE_REQUEST_UPDATE,
  ],
};

export class PermissionService {
  // Get all available permissions
  static getAllPermissions(): typeof PERMISSIONS {
    return PERMISSIONS;
  }

  // Get permission groups
  static getPermissionGroups(): typeof PERMISSION_GROUPS {
    return PERMISSION_GROUPS;
  }

  // Get default permissions for a role
  static getDefaultPermissions(role: string): string[] {
    return DEFAULT_ROLE_PERMISSIONS[role as keyof typeof DEFAULT_ROLE_PERMISSIONS] || [];
  }

  // Check if user has permission
  static async userHasPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const result = await query(
        `SELECT r.permissions 
         FROM users u
         JOIN profiles p ON u.id = p.id
         JOIN roles r ON r.name = p.role
         WHERE u.id = $1 AND r.is_active = true`,
        [userId]
      );

      if (result.rows.length === 0) {
        return false;
      }

      const permissions = result.rows[0].permissions || [];
      return permissions.includes(permission);
    } catch (error) {
      console.error('Check user permission error:', error);
      return false;
    }
  }

  // Check if user has any of the permissions
  static async userHasAnyPermission(userId: string, permissions: string[]): Promise<boolean> {
    try {
      const result = await query(
        `SELECT r.permissions 
         FROM users u
         JOIN profiles p ON u.id = p.id
         JOIN roles r ON r.name = p.role
         WHERE u.id = $1 AND r.is_active = true`,
        [userId]
      );

      if (result.rows.length === 0) {
        return false;
      }

      const userPermissions = result.rows[0].permissions || [];
      return permissions.some(permission => userPermissions.includes(permission));
    } catch (error) {
      console.error('Check user permissions error:', error);
      return false;
    }
  }

  // Get user permissions
  static async getUserPermissions(userId: string): Promise<string[]> {
    try {
      const result = await query(
        `SELECT r.permissions 
         FROM users u
         JOIN profiles p ON u.id = p.id
         JOIN roles r ON r.name = p.role
         WHERE u.id = $1 AND r.is_active = true`,
        [userId]
      );

      if (result.rows.length === 0) {
        return [];
      }

      return result.rows[0].permissions || [];
    } catch (error) {
      console.error('Get user permissions error:', error);
      return [];
    }
  }

  // Update role permissions
  static async updateRolePermissions(roleId: string, permissions: string[]): Promise<void> {
    try {
      await query(
        'UPDATE roles SET permissions = $1, updated_at = NOW() WHERE id = $2',
        [JSON.stringify(permissions), roleId]
      );
    } catch (error) {
      console.error('Update role permissions error:', error);
      throw new Error('Failed to update role permissions');
    }
  }

  // Validate permissions
  static validatePermissions(permissions: string[]): { valid: string[]; invalid: string[] } {
    const allPermissions = Object.values(PERMISSIONS);
    const valid: string[] = [];
    const invalid: string[] = [];

    permissions.forEach(permission => {
      if (allPermissions.includes(permission as any)) {
        valid.push(permission);
      } else {
        invalid.push(permission);
      }
    });

    return { valid, invalid };
  }
}
