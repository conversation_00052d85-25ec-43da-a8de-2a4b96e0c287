
-- Create roles table for flexible role management
CREATE TABLE public.roles (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  permissions JSONB,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create departments table for flexible department management
CREATE TABLE public.departments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  manager_id UUID REFERENCES public.employees(id),
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create positions table for flexible position management
CREATE TABLE public.positions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  department_id UUID REFERENCES public.departments(id),
  role_id UUID REFERENCES public.roles(id),
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Insert default roles
INSERT INTO public.roles (name, description) VALUES
('admin', 'System Administrator'),
('doctor', 'Medical Doctor'),
('nurse', 'Registered Nurse'),
('pharmacist', 'Licensed Pharmacist'),
('technician', 'Medical Technician'),
('receptionist', 'Front Desk Receptionist'),
('manager', 'Department Manager');

-- Insert default departments
INSERT INTO public.departments (name, description) VALUES
('emergency', 'Emergency Department'),
('surgery', 'Surgery Department'),
('pediatrics', 'Pediatrics Department'),
('cardiology', 'Cardiology Department'),
('orthopedics', 'Orthopedics Department'),
('pharmacy', 'Pharmacy Department'),
('laboratory', 'Laboratory Department'),
('radiology', 'Radiology Department'),
('administration', 'Administration Department'),
('maintenance', 'Maintenance Department');

-- Insert default positions
INSERT INTO public.positions (name, description, department_id, role_id) 
SELECT 
  'Head of ' || d.name,
  'Department head for ' || d.name,
  d.id,
  r.id
FROM public.departments d
CROSS JOIN public.roles r
WHERE r.name = 'manager'
LIMIT 10;

-- Add RLS policies for roles table
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view active roles" ON public.roles FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage roles" ON public.roles FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Add RLS policies for departments table
ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view active departments" ON public.departments FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage departments" ON public.departments FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Add RLS policies for positions table
ALTER TABLE public.positions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view active positions" ON public.positions FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage positions" ON public.positions FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
