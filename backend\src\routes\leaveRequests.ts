// Leave request routes
import { Router, Response } from 'express';
import { LeaveRequestService } from '@/services/leaveRequestService';
import { authenticateToken, requireManagerOrAdmin, AuthRequest } from '@/middleware/auth';
import { validate, leaveRequestSchemas } from '@/middleware/validation';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all leave requests
router.get('/', async (req: AuthRequest, res: Response) => {
  try {
    const { employeeId, status } = req.query;

    let leaveRequests;

    if (employeeId) {
      // Users can view their own leave requests, managers/admins can view any
      if (req.user!.role !== 'admin' && req.user!.role !== 'manager') {
        // TODO: Check if user is requesting their own leave requests
        // This would require additional logic to map user ID to employee ID
      }
      leaveRequests = await LeaveRequestService.getByEmployeeId(employeeId as string);
    } else if (status) {
      leaveRequests = await LeaveRequestService.getByStatus(status as string);
    } else {
      leaveRequests = await LeaveRequestService.getAll();
    }

    res.json({
      leaveRequests,
      total: leaveRequests.length
    });
  } catch (error) {
    console.error('Get leave requests endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch leave requests' });
  }
});

// Get leave request statistics (Manager/Admin only)
router.get('/statistics', requireManagerOrAdmin, async (req: AuthRequest, res: Response) => {
  try {
    const statistics = await LeaveRequestService.getStatistics();
    res.json(statistics);
  } catch (error) {
    console.error('Get leave request statistics endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch leave request statistics' });
  }
});

// Get leave request by ID
router.get('/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const leaveRequest = await LeaveRequestService.getById(id);
    
    if (!leaveRequest) {
      return res.status(404).json({ error: 'Leave request not found' });
    }

    // Check if user can access this leave request
    // Users can view their own leave requests, managers/admins can view any
    // TODO: Add logic to check if the leave request belongs to the user

    res.json({ leaveRequest });
  } catch (error) {
    console.error('Get leave request by ID endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch leave request' });
  }
});

// Create leave request
router.post('/', validate(leaveRequestSchemas.create), async (req: AuthRequest, res: Response) => {
  try {
    const leaveData = {
      employee_id: req.body.employeeId,
      leave_type: req.body.leaveType,
      start_date: new Date(req.body.startDate),
      end_date: new Date(req.body.endDate),
      reason: req.body.reason,
      status: req.body.status || 'pending',
      approved_by: req.body.approvedBy,
    };

    // Validate date range
    if (leaveData.start_date >= leaveData.end_date) {
      return res.status(400).json({ error: 'End date must be after start date' });
    }

    // Check if user can create leave request for this employee
    // Users can only create leave requests for themselves (unless admin/manager)
    if (req.user!.role !== 'admin' && req.user!.role !== 'manager') {
      // TODO: Check if employeeId belongs to the current user
      // For now, we'll allow all authenticated users to create leave requests
    }

    const leaveRequest = await LeaveRequestService.create(leaveData);

    res.status(201).json({
      message: 'Leave request created successfully',
      leaveRequest
    });
  } catch (error) {
    console.error('Create leave request endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({ error: error.message });
      }
      if (error.message.includes('overlapping')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to create leave request' });
  }
});

// Update leave request
router.put('/:id', validate(leaveRequestSchemas.update), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    // Check if leave request exists
    const existingRequest = await LeaveRequestService.getById(id);
    if (!existingRequest) {
      return res.status(404).json({ error: 'Leave request not found' });
    }

    // Check permissions
    const canUpdate = 
      req.user!.role === 'admin' || 
      req.user!.role === 'manager' ||
      (existingRequest.status === 'pending'); // Users can update their own pending requests

    if (!canUpdate) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Convert camelCase to snake_case for database
    const updateData: any = {};
    if (req.body.leaveType !== undefined) updateData.leave_type = req.body.leaveType;
    if (req.body.startDate !== undefined) updateData.start_date = new Date(req.body.startDate);
    if (req.body.endDate !== undefined) updateData.end_date = new Date(req.body.endDate);
    if (req.body.reason !== undefined) updateData.reason = req.body.reason;
    if (req.body.status !== undefined) updateData.status = req.body.status;

    // Validate date range if dates are being updated
    if (updateData.start_date && updateData.end_date && updateData.start_date >= updateData.end_date) {
      return res.status(400).json({ error: 'End date must be after start date' });
    }

    // Only managers/admins can approve/reject requests
    if (updateData.status && (updateData.status === 'approved' || updateData.status === 'rejected')) {
      if (req.user!.role !== 'admin' && req.user!.role !== 'manager') {
        return res.status(403).json({ error: 'Only managers and admins can approve/reject leave requests' });
      }
    }

    const leaveRequest = await LeaveRequestService.update(
      id, 
      updateData, 
      (updateData.status === 'approved' || updateData.status === 'rejected') ? req.user!.id : undefined
    );

    res.json({
      message: 'Leave request updated successfully',
      leaveRequest
    });
  } catch (error) {
    console.error('Update leave request endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Leave request not found') {
        return res.status(404).json({ error: error.message });
      }
      if (error.message.includes('overlapping')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to update leave request' });
  }
});

// Delete leave request
router.delete('/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    // Check if leave request exists
    const existingRequest = await LeaveRequestService.getById(id);
    if (!existingRequest) {
      return res.status(404).json({ error: 'Leave request not found' });
    }

    // Check permissions
    const canDelete = 
      req.user!.role === 'admin' || 
      req.user!.role === 'manager' ||
      (existingRequest.status === 'pending'); // Users can delete their own pending requests

    if (!canDelete) {
      return res.status(403).json({ error: 'Access denied' });
    }

    await LeaveRequestService.delete(id);

    res.json({ message: 'Leave request deleted successfully' });
  } catch (error) {
    console.error('Delete leave request endpoint error:', error);
    
    if (error instanceof Error && error.message === 'Leave request not found') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to delete leave request' });
  }
});

// Approve leave request (Manager/Admin only)
router.patch('/:id/approve', requireManagerOrAdmin, async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    const leaveRequest = await LeaveRequestService.update(
      id, 
      { status: 'approved' }, 
      req.user!.id
    );

    res.json({
      message: 'Leave request approved successfully',
      leaveRequest
    });
  } catch (error) {
    console.error('Approve leave request endpoint error:', error);
    
    if (error instanceof Error && error.message === 'Leave request not found') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to approve leave request' });
  }
});

// Reject leave request (Manager/Admin only)
router.patch('/:id/reject', requireManagerOrAdmin, async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    const leaveRequest = await LeaveRequestService.update(
      id, 
      { status: 'rejected' }, 
      req.user!.id
    );

    res.json({
      message: 'Leave request rejected successfully',
      leaveRequest
    });
  } catch (error) {
    console.error('Reject leave request endpoint error:', error);
    
    if (error instanceof Error && error.message === 'Leave request not found') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to reject leave request' });
  }
});

export default router;
