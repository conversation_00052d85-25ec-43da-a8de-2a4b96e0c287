// API client for communicating with Express.js backend
import axios, { AxiosInstance } from 'axios';

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
const API_PREFIX = '/api/v1';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}${API_PREFIX}`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Token storage
export const tokenStorage = {
  get: (): string | null => {
    return localStorage.getItem('auth_token');
  },
  set: (token: string): void => {
    localStorage.setItem('auth_token', token);
  },
  remove: (): void => {
    localStorage.removeItem('auth_token');
  },
};

// Rate limiting and refresh token management
let isRefreshing = false;
let refreshSubscribers: ((token: string | null) => void)[] = [];

const subscribeTokenRefresh = (cb: (token: string | null) => void) => {
  refreshSubscribers.push(cb);
};

const onRefreshed = (token: string | null) => {
  refreshSubscribers.forEach(cb => cb(token));
  refreshSubscribers = [];
};

// Request queue to prevent too many requests
const requestQueue: (() => Promise<any>)[] = [];
let isProcessingQueue = false;

const processRequestQueue = async () => {
  if (isProcessingQueue) return;
  isProcessingQueue = true;

  while (requestQueue.length > 0) {
    const request = requestQueue.shift();
    if (request) {
      try {
        await request();
        // Add small delay between requests to prevent rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error('Queued request failed:', error);
      }
    }
  }

  isProcessingQueue = false;
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = tokenStorage.get();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Don't retry for auth endpoints to avoid infinite loops
    if (error.response?.status === 401 &&
        !originalRequest._retry &&
        !originalRequest.url?.includes('/auth/login') &&
        !originalRequest.url?.includes('/auth/register') &&
        !originalRequest.url?.includes('/auth/refresh')) {

      originalRequest._retry = true;

      if (isRefreshing) {
        // If already refreshing, wait for the result
        return new Promise((resolve) => {
          subscribeTokenRefresh((token) => {
            if (token) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              resolve(api(originalRequest));
            } else {
              resolve(Promise.reject(error));
            }
          });
        });
      }

      isRefreshing = true;

      try {
        // Try to refresh token using a separate axios instance to avoid interceptor loop
        const refreshResponse = await axios.post(`${API_BASE_URL}${API_PREFIX}/auth/refresh`, {}, {
          withCredentials: true,
          timeout: 10000,
        });

        const { access_token } = refreshResponse.data;

        tokenStorage.set(access_token);
        originalRequest.headers.Authorization = `Bearer ${access_token}`;

        onRefreshed(access_token);

        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        console.error('Token refresh failed:', refreshError);
        tokenStorage.remove();
        onRefreshed(null);

        // Avoid immediate redirect if already on auth page
        if (!window.location.pathname.includes('/auth')) {
          // Use setTimeout to avoid immediate redirect during page load
          setTimeout(() => {
            window.location.href = '/auth';
          }, 100);
        }

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

// Wrapper function to add requests to queue if needed
const queueRequest = async <T>(requestFn: () => Promise<T>): Promise<T> => {
  if (requestQueue.length > 10) {
    throw new Error('Too many requests queued. Please try again later.');
  }

  return new Promise((resolve, reject) => {
    const queuedRequest = async () => {
      try {
        const result = await requestFn();
        resolve(result);
      } catch (error) {
        reject(error);
      }
    };

    requestQueue.push(queuedRequest);
    processRequestQueue();
  });
};

// API response types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
}

export interface ApiError {
  error: string;
  details?: string;
  fields?: string[];
}

// Authentication API
export const authApi = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return {
      data: {
        user: response.data.user,
        access_token: response.data.access_token,
        expires_in: response.data.expires_in
      }
    };
  },

  register: async (
    email: string,
    password: string,
    firstName?: string,
    lastName?: string,
    role?: string
  ) => {
    const response = await api.post('/auth/register', {
      email,
      password,
      firstName,
      lastName,
      role,
    });
    return {
      data: {
        user: response.data.user
      }
    };
  },

  logout: async () => {
    const response = await api.post('/auth/logout');
    return {
      data: response.data
    };
  },

  getCurrentUser: async () => {
    return queueRequest(() =>
      api.get('/auth/me').then(res => ({
        data: {
          user: res.data.user
        }
      }))
    );
  },

  updateProfile: async (profileData: any): Promise<ApiResponse<{ profile: any }>> => {
    const response = await api.put('/auth/profile', profileData);
    return response.data;
  },

  changePassword: async (currentPassword: string, newPassword: string): Promise<ApiResponse> => {
    const response = await api.put('/auth/change-password', {
      currentPassword,
      newPassword,
    });
    return response.data;
  },

  verifyToken: async (): Promise<ApiResponse<{ valid: boolean; user: any }>> => {
    return queueRequest(() => api.get('/auth/verify').then(res => res.data));
  },
};

// Employee API
export const employeeApi = {
  getAll: async (params?: {
    department?: string;
    role?: string;
    search?: string;
  }): Promise<ApiResponse<{ employees: any[]; total: number }>> => {
    return queueRequest(() => api.get('/employees', { params }).then(res => res.data));
  },

  getById: async (id: string): Promise<ApiResponse<{ employee: any }>> => {
    return queueRequest(() => api.get(`/employees/${id}`).then(res => res.data));
  },

  create: async (employeeData: any): Promise<ApiResponse<{ employee: any }>> => {
    const response = await api.post('/employees', employeeData);
    return response.data;
  },

  update: async (id: string, employeeData: any): Promise<ApiResponse<{ employee: any }>> => {
    const response = await api.put(`/employees/${id}`, employeeData);
    return response.data;
  },

  delete: async (id: string): Promise<ApiResponse> => {
    const response = await api.delete(`/employees/${id}`);
    return response.data;
  },

  getStatistics: async (): Promise<ApiResponse<any>> => {
    return queueRequest(() => api.get('/employees/statistics').then(res => res.data));
  },
};

// Schedule API
export const scheduleApi = {
  getAll: async (params?: {
    employeeId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<{ schedules: any[]; total: number }>> => {
    return queueRequest(() => api.get('/schedules', { params }).then(res => res.data));
  },

  getById: async (id: string): Promise<ApiResponse<{ schedule: any }>> => {
    return queueRequest(() => api.get(`/schedules/${id}`).then(res => res.data));
  },

  create: async (scheduleData: any): Promise<ApiResponse<{ schedule: any }>> => {
    const response = await api.post('/schedules', scheduleData);
    return response.data;
  },

  update: async (id: string, scheduleData: any): Promise<ApiResponse<{ schedule: any }>> => {
    const response = await api.put(`/schedules/${id}`, scheduleData);
    return response.data;
  },

  delete: async (id: string): Promise<ApiResponse> => {
    const response = await api.delete(`/schedules/${id}`);
    return response.data;
  },

  getStatistics: async (params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<any>> => {
    return queueRequest(() => api.get('/schedules/statistics', { params }).then(res => res.data));
  },
};

// Leave Request API
export const leaveRequestApi = {
  getAll: async (params?: {
    employeeId?: string;
    status?: string;
  }): Promise<ApiResponse<{ leaveRequests: any[]; total: number }>> => {
    return queueRequest(() => api.get('/leave-requests', { params }).then(res => res.data));
  },

  getById: async (id: string): Promise<ApiResponse<{ leaveRequest: any }>> => {
    return queueRequest(() => api.get(`/leave-requests/${id}`).then(res => res.data));
  },

  create: async (leaveRequestData: any): Promise<ApiResponse<{ leaveRequest: any }>> => {
    const response = await api.post('/leave-requests', leaveRequestData);
    return response.data;
  },

  update: async (id: string, leaveRequestData: any): Promise<ApiResponse<{ leaveRequest: any }>> => {
    const response = await api.put(`/leave-requests/${id}`, leaveRequestData);
    return response.data;
  },

  delete: async (id: string): Promise<ApiResponse> => {
    const response = await api.delete(`/leave-requests/${id}`);
    return response.data;
  },

  approve: async (id: string): Promise<ApiResponse<{ leaveRequest: any }>> => {
    const response = await api.patch(`/leave-requests/${id}/approve`);
    return response.data;
  },

  reject: async (id: string): Promise<ApiResponse<{ leaveRequest: any }>> => {
    const response = await api.patch(`/leave-requests/${id}/reject`);
    return response.data;
  },

  getStatistics: async (): Promise<ApiResponse<any>> => {
    return queueRequest(() => api.get('/leave-requests/statistics').then(res => res.data));
  },
};

// Management API
export const managementApi = {
  // Departments
  getDepartments: async (): Promise<ApiResponse<{ departments: any[] }>> => {
    return queueRequest(() => api.get('/management/departments').then(res => res.data));
  },

  createDepartment: async (departmentData: any): Promise<ApiResponse<{ department: any }>> => {
    const response = await api.post('/management/departments', departmentData);
    return response.data;
  },

  updateDepartment: async (id: string, departmentData: any): Promise<ApiResponse<{ department: any }>> => {
    const response = await api.put(`/management/departments/${id}`, departmentData);
    return response.data;
  },

  deleteDepartment: async (id: string): Promise<ApiResponse> => {
    const response = await api.delete(`/management/departments/${id}`);
    return response.data;
  },

  // Roles
  getRoles: async (): Promise<ApiResponse<{ roles: any[] }>> => {
    return queueRequest(() => api.get('/management/roles').then(res => res.data));
  },

  createRole: async (roleData: any): Promise<ApiResponse<{ role: any }>> => {
    const response = await api.post('/management/roles', roleData);
    return response.data;
  },

  updateRole: async (id: string, roleData: any): Promise<ApiResponse<{ role: any }>> => {
    const response = await api.put(`/management/roles/${id}`, roleData);
    return response.data;
  },

  // Positions
  getPositions: async (): Promise<ApiResponse<{ positions: any[] }>> => {
    return queueRequest(() => api.get('/management/positions').then(res => res.data));
  },

  createPosition: async (positionData: any): Promise<ApiResponse<{ position: any }>> => {
    const response = await api.post('/management/positions', positionData);
    return response.data;
  },

  updatePosition: async (id: string, positionData: any): Promise<ApiResponse<{ position: any }>> => {
    const response = await api.put(`/management/positions/${id}`, positionData);
    return response.data;
  },
};

// Admin/User Management API
export const adminApi = {
  // Users
  getUsers: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<ApiResponse<{ users: any[]; total: number; pagination: any }>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const response = await api.get(`/admin/users?${queryParams.toString()}`);
    return response.data;
  },

  getUser: async (id: string): Promise<ApiResponse<{ user: any }>> => {
    const response = await api.get(`/admin/users/${id}`);
    return response.data;
  },

  createUser: async (userData: {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
    role?: string;
    emailConfirmed?: boolean;
  }): Promise<ApiResponse<{ user: any }>> => {
    const response = await api.post('/admin/users', userData);
    return response.data;
  },

  updateUser: async (id: string, userData: {
    firstName?: string;
    lastName?: string;
    email?: string;
    role?: string;
    emailConfirmed?: boolean;
  }): Promise<ApiResponse<{ message: string }>> => {
    const response = await api.put(`/admin/users/${id}`, userData);
    return response.data;
  },

  updateUserStatus: async (id: string, status: {
    suspended?: boolean;
  }): Promise<ApiResponse<{ message: string }>> => {
    const response = await api.put(`/admin/users/${id}/status`, status);
    return response.data;
  },

  deleteUser: async (id: string): Promise<ApiResponse<{ message: string }>> => {
    const response = await api.delete(`/admin/users/${id}`);
    return response.data;
  },

  // Bulk operations
  bulkCreateUsers: async (users: Array<{
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
    role?: string;
  }>): Promise<ApiResponse<{ created: any[]; errors: any[] }>> => {
    const response = await api.post('/admin/users/bulk', { users });
    return response.data;
  },

  bulkUpdateUsers: async (updates: Array<{
    id: string;
    suspended?: boolean;
    emailConfirmed?: boolean;
    role?: string;
  }>): Promise<ApiResponse<{ updated: string[]; errors: any[] }>> => {
    const response = await api.put('/admin/users/bulk', { updates });
    return response.data;
  },

  bulkDeleteUsers: async (userIds: string[]): Promise<ApiResponse<{ deleted: string[]; errors: any[] }>> => {
    const response = await api.delete('/admin/users/bulk', { data: { userIds } });
    return response.data;
  },

  // User management actions
  resetUserPassword: async (id: string, newPassword: string): Promise<ApiResponse<{ message: string }>> => {
    const response = await api.post(`/admin/users/${id}/reset-password`, { newPassword });
    return response.data;
  },

  unlockUser: async (id: string): Promise<ApiResponse<{ message: string }>> => {
    const response = await api.post(`/admin/users/${id}/unlock`);
    return response.data;
  },

  getUserActivity: async (id: string, params?: {
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<{ logs: any[] }>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const response = await api.get(`/admin/users/${id}/activity?${queryParams.toString()}`);
    return response.data;
  },

  getActivityLogs: async (params?: {
    limit?: number;
    offset?: number;
    userId?: string;
    action?: string;
    resource?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<{ logs: any[] }>> => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const response = await api.get(`/admin/activity-logs?${queryParams.toString()}`);
    return response.data;
  },

  getStats: async (): Promise<ApiResponse<{ statistics: any }>> => {
    const response = await api.get('/admin/stats');
    return response.data;
  },
};

export default api;