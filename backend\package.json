{"name": "<PERSON><PERSON>-ka<PERSON><PERSON>-backend", "version": "1.0.0", "description": "Express.js backend for <PERSON><PERSON>", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "migrate": "node dist/scripts/migrate.js"}, "keywords": ["express", "postgresql", "typescript", "api"], "author": "Your Name", "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.1", "bcryptjs": "^2.4.3", "cjs": "^0.0.11", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.11.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/pg": "^8.10.9", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}}