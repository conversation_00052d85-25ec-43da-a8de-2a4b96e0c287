
export type EmployeeRole = 'admin' | 'doctor' | 'nurse' | 'pharmacist' | 'technician' | 'receptionist' | 'manager';

export type EmployeeStatus = 'active' | 'inactive' | 'on_leave' | 'terminated';

export type ShiftType = 'morning' | 'afternoon' | 'night' | 'rotating' | 'regular';

// Export as Shift for backward compatibility
export type Shift = ShiftType;

export type DepartmentType = 
  | 'emergency' 
  | 'surgery' 
  | 'pediatrics' 
  | 'cardiology' 
  | 'orthopedics' 
  | 'pharmacy' 
  | 'laboratory' 
  | 'radiology' 
  | 'administration' 
  | 'maintenance';

// Export as Department for backward compatibility
export type Department = DepartmentType;

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
  [key: string]: string | undefined; // Make it compatible with Json type
}

export interface Employee {
  id: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: EmployeeRole;
  department: DepartmentType;
  position: string;
  joinDate: string;
  status: EmployeeStatus;
  shift: ShiftType;
  salary?: number;
  avatar?: string;
  address?: string;
  emergencyContact?: EmergencyContact;
  certifications: string[];
  skills: string[];
  userId?: string; // Add userId field to link with auth.users
}

// Add EmployeeFilters interface
export interface EmployeeFilters {
  search?: string;
  role?: EmployeeRole;
  department?: DepartmentType;
  status?: EmployeeStatus;
  shift?: ShiftType;
}
