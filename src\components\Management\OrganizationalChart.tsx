import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useQuery } from '@tanstack/react-query';
import { managementApi } from '@/lib/api';
import { 
  Building, 
  Users, 
  User, 
  ChevronDown, 
  ChevronRight,
  MapPin,
  Briefcase,
  TrendingUp,
  BarChart3
} from 'lucide-react';

interface DepartmentNodeProps {
  department: any;
  employees: any[];
  level: number;
  onSelectDepartment: (dept: any) => void;
}

function DepartmentNode({ department, employees, level, onSelectDepartment }: DepartmentNodeProps) {
  const [isExpanded, setIsExpanded] = useState(level < 2);
  
  const departmentEmployees = employees.filter(emp => emp.department_id === department.id);
  const hasSubdepartments = level < 3; // Simplified for demo

  return (
    <div className={`ml-${level * 6} mb-4`}>
      <Card className="border-l-4 border-l-blue-500">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {hasSubdepartments && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                </Button>
              )}
              <Building className="h-5 w-5 text-blue-600" />
              <div>
                <h4 className="font-semibold">{department.name}</h4>
                <p className="text-sm text-gray-600">{department.description}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                <Users className="h-3 w-3 mr-1" />
                {departmentEmployees.length} karyawan
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onSelectDepartment(department)}
              >
                Detail
              </Button>
            </div>
          </div>

          {department.manager_first_name && (
            <div className="mt-2 flex items-center gap-2 text-sm text-gray-600">
              <User className="h-4 w-4" />
              Manager: {department.manager_first_name} {department.manager_last_name}
            </div>
          )}

          {isExpanded && departmentEmployees.length > 0 && (
            <div className="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {departmentEmployees.slice(0, 6).map((employee: any) => (
                <div key={employee.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                  <User className="h-4 w-4 text-gray-400" />
                  <div className="text-sm">
                    <div className="font-medium">{employee.first_name} {employee.last_name}</div>
                    <div className="text-gray-500">{employee.position_name}</div>
                  </div>
                </div>
              ))}
              {departmentEmployees.length > 6 && (
                <div className="flex items-center justify-center p-2 bg-gray-100 rounded text-sm text-gray-600">
                  +{departmentEmployees.length - 6} lainnya
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export function OrganizationalChart() {
  const [selectedDepartment, setSelectedDepartment] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'hierarchy' | 'stats'>('hierarchy');

  const { data: orgChart, isLoading: isLoadingChart } = useQuery({
    queryKey: ['organizational-chart'],
    queryFn: async () => {
      const response = await managementApi.getOrganizationalChart();
      return response;
    },
  });

  const { data: orgStats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['organizational-stats'],
    queryFn: async () => {
      const response = await managementApi.getOrganizationalStats();
      return response.stats;
    },
  });

  const { data: departmentEmployees } = useQuery({
    queryKey: ['department-employees', selectedDepartment?.id],
    queryFn: async () => {
      if (!selectedDepartment?.id) return null;
      const response = await managementApi.getDepartmentEmployees(selectedDepartment.id);
      return response.employees;
    },
    enabled: !!selectedDepartment?.id,
  });

  if (isLoadingChart || isLoadingStats) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  const departments = orgChart?.departments || [];
  const employees = orgChart?.employees || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Struktur Organisasi</h2>
          <p className="text-gray-600">Visualisasi hierarki departemen dan karyawan</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="hierarchy">Hierarki</SelectItem>
              <SelectItem value="stats">Statistik</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {viewMode === 'hierarchy' ? (
        <div className="space-y-4">
          {departments.map((department: any) => (
            <DepartmentNode
              key={department.id}
              department={department}
              employees={employees}
              level={department.level}
              onSelectDepartment={setSelectedDepartment}
            />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Departemen
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-2xl font-bold">{orgStats?.departmentStats?.total_departments || 0}</div>
                <div className="text-sm text-gray-600">
                  {orgStats?.departmentStats?.departments_with_managers || 0} dengan manager
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                Posisi
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-2xl font-bold">{orgStats?.positionStats?.total_positions || 0}</div>
                <div className="text-sm text-gray-600">
                  {orgStats?.positionStats?.subordinate_positions || 0} posisi bawahan
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Karyawan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-2xl font-bold">{employees.length}</div>
                <div className="text-sm text-gray-600">Total karyawan aktif</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Rata-rata Gaji
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-2xl font-bold">
                  {orgStats?.positionStats?.avg_salary_range 
                    ? `Rp ${Math.round(orgStats.positionStats.avg_salary_range).toLocaleString('id-ID')}`
                    : 'N/A'
                  }
                </div>
                <div className="text-sm text-gray-600">Range gaji rata-rata</div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {viewMode === 'stats' && orgStats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Distribusi Karyawan per Departemen
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {orgStats.employeeDistribution?.map((dept: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">{dept.department_name}</div>
                      <div className="text-sm text-gray-600">{dept.manager_count} manager</div>
                    </div>
                    <Badge variant="outline">{dept.employee_count} karyawan</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Distribusi Role
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {orgStats.roleDistribution?.map((role: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="font-medium capitalize">{role.role}</div>
                    <Badge variant="outline">{role.count} orang</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Department Detail Dialog */}
      <Dialog open={!!selectedDepartment} onOpenChange={() => setSelectedDepartment(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              {selectedDepartment?.name}
            </DialogTitle>
          </DialogHeader>
          
          {selectedDepartment && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Informasi Departemen</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>Nama:</strong> {selectedDepartment.name}</div>
                    <div><strong>Deskripsi:</strong> {selectedDepartment.description || 'Tidak ada'}</div>
                    <div>
                      <strong>Manager:</strong> {
                        selectedDepartment.manager_first_name 
                          ? `${selectedDepartment.manager_first_name} ${selectedDepartment.manager_last_name}`
                          : 'Belum ditentukan'
                      }
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Statistik</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>Total Karyawan:</strong> {departmentEmployees?.length || 0}</div>
                    <div><strong>Level:</strong> {selectedDepartment.level}</div>
                  </div>
                </div>
              </div>

              {departmentEmployees && departmentEmployees.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Daftar Karyawan</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-64 overflow-y-auto">
                    {departmentEmployees.map((employee: any) => (
                      <div key={employee.id} className="flex items-center gap-2 p-2 border rounded">
                        <User className="h-4 w-4 text-gray-400" />
                        <div className="text-sm">
                          <div className="font-medium">{employee.first_name} {employee.last_name}</div>
                          <div className="text-gray-500">{employee.position_name}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
