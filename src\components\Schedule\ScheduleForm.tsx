
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useSchedules } from '@/hooks/useSchedulesNew';
import { useEmployees } from '@/hooks/useEmployeesNew';

const scheduleSchema = z.object({
  employeeId: z.string().min(1, '<PERSON><PERSON><PERSON> karyawan'),
  shiftDate: z.string().min(1, 'Tanggal wajib diisi'),
  shiftType: z.enum(['morning', 'afternoon', 'night', 'rotating', 'regular']),
  startTime: z.string().min(1, 'Jam mulai wajib diisi'),
  endTime: z.string().min(1, 'Jam selesai wajib diisi'),
  notes: z.string().optional(),
});

type ScheduleFormData = z.infer<typeof scheduleSchema>;

interface ScheduleFormProps {
  onSuccess?: () => void;
}

export function ScheduleForm({ onSuccess }: ScheduleFormProps) {
  const { createSchedule, isCreating: isCreatingSchedule } = useSchedules();
  const { employees } = useEmployees();
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm<ScheduleFormData>({
    resolver: zodResolver(scheduleSchema),
  });

  const shiftType = watch('shiftType');

  // Set default times based on shift type
  const handleShiftTypeChange = (value: string) => {
    setValue('shiftType', value as any);
    
    switch (value) {
      case 'morning':
        setValue('startTime', '07:00');
        setValue('endTime', '15:00');
        break;
      case 'afternoon':
        setValue('startTime', '15:00');
        setValue('endTime', '23:00');
        break;
      case 'night':
        setValue('startTime', '23:00');
        setValue('endTime', '07:00');
        break;
      case 'regular':
        setValue('startTime', '08:00');
        setValue('endTime', '16:00');
        break;
      default:
        break;
    }
  };

  const onSubmit = (data: ScheduleFormData) => {
    // Ensure all required fields are present by creating a properly typed object
    const scheduleData = {
      employeeId: data.employeeId,
      shiftDate: data.shiftDate,
      shiftType: data.shiftType,
      startTime: data.startTime,
      endTime: data.endTime,
      notes: data.notes,
      status: 'scheduled' as const,
    };
    
    createSchedule(scheduleData);
    reset();
    onSuccess?.();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="employeeId">Karyawan *</Label>
        <Select onValueChange={(value) => setValue('employeeId', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Pilih karyawan" />
          </SelectTrigger>
          <SelectContent>
            {employees.map((employee) => (
              <SelectItem key={employee.id} value={employee.id}>
                {employee.firstName} {employee.lastName} - {employee.department}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.employeeId && (
          <p className="text-sm text-red-600">{errors.employeeId.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="shiftDate">Tanggal *</Label>
        <Input
          id="shiftDate"
          type="date"
          {...register('shiftDate')}
        />
        {errors.shiftDate && (
          <p className="text-sm text-red-600">{errors.shiftDate.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="shiftType">Tipe Shift *</Label>
        <Select onValueChange={handleShiftTypeChange}>
          <SelectTrigger>
            <SelectValue placeholder="Pilih tipe shift" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="morning">Pagi (07:00 - 15:00)</SelectItem>
            <SelectItem value="afternoon">Siang (15:00 - 23:00)</SelectItem>
            <SelectItem value="night">Malam (23:00 - 07:00)</SelectItem>
            <SelectItem value="regular">Reguler (08:00 - 16:00)</SelectItem>
            <SelectItem value="rotating">Bergilir</SelectItem>
          </SelectContent>
        </Select>
        {errors.shiftType && (
          <p className="text-sm text-red-600">{errors.shiftType.message}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="startTime">Jam Mulai *</Label>
          <Input
            id="startTime"
            type="time"
            {...register('startTime')}
          />
          {errors.startTime && (
            <p className="text-sm text-red-600">{errors.startTime.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="endTime">Jam Selesai *</Label>
          <Input
            id="endTime"
            type="time"
            {...register('endTime')}
          />
          {errors.endTime && (
            <p className="text-sm text-red-600">{errors.endTime.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Catatan</Label>
        <Textarea
          id="notes"
          placeholder="Catatan tambahan (opsional)"
          {...register('notes')}
        />
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={isCreatingSchedule}>
          {isCreatingSchedule ? 'Menyimpan...' : 'Simpan Jadwal'}
        </Button>
      </div>
    </form>
  );
}
