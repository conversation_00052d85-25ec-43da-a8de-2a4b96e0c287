
import { Card, CardContent } from '@/components/ui/card';
import { Clock, Users, Calendar, Filter } from 'lucide-react';
import { Schedule, LeaveRequest } from '@/types/schedule';
import { Employee } from '@/types/employee';

interface ScheduleStatsProps {
  schedules: Schedule[];
  employees: Employee[];
  leaveRequests: LeaveRequest[];
}

export function ScheduleStats({ schedules, employees, leaveRequests }: ScheduleStatsProps) {
  const todaySchedules = schedules.filter(schedule => 
    schedule.shiftDate === new Date().toISOString().split('T')[0]
  );

  const pendingLeaveRequests = leaveRequests.filter(request => 
    request.status === 'pending'
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-gray-600"><PERSON><PERSON><PERSON> Hari Ini</p>
              <p className="text-2xl font-bold">{todaySchedules.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-green-600" />
            <div>
              <p className="text-sm font-medium text-gray-600">Total Karyawan</p>
              <p className="text-2xl font-bold">{employees.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-orange-600" />
            <div>
              <p className="text-sm font-medium text-gray-600">Cuti Pending</p>
              <p className="text-2xl font-bold">{pendingLeaveRequests.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-purple-600" />
            <div>
              <p className="text-sm font-medium text-gray-600">Total Jadwal</p>
              <p className="text-2xl font-bold">{schedules.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
