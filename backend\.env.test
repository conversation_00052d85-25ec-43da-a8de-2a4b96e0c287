# Test Environment Configuration
NODE_ENV=test

# Test Database Configuration
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_NAME=manajemen_karyawan_test
TEST_DB_USER=postgres
TEST_DB_PASSWORD=

# JWT Configuration for Testing
JWT_SECRET=test-jwt-secret-key-for-testing-only
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=test-refresh-token-secret-key
REFRESH_TOKEN_EXPIRES_IN=30d

# Server Configuration
PORT=3002
API_PREFIX=/api/v1

# CORS Configuration
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Rate Limiting (more lenient for testing)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
BCRYPT_ROUNDS=4

# Database Pool Settings (smaller for testing)
DB_POOL_MAX=5
DB_POOL_MIN=1
DB_IDLE_TIMEOUT=10000
DB_CONNECTION_TIMEOUT=2000
DB_ACQUIRE_TIMEOUT=10000
DB_STATEMENT_TIMEOUT=10000
DB_QUERY_TIMEOUT=10000
